#!/usr/bin/env node

/**
 * Test script for UrbanEdge AI Assistant API
 * This script tests all API endpoints to ensure they're working correctly
 */

import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3001/api';
const API_KEY = 'urbanedge-ai-assistant-key-2024';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const headers = {
    'X-API-Key': API_KEY,
    'Content-Type': 'application/json',
    ...options.headers
  };

  try {
    const response = await fetch(url, {
      ...options,
      headers
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  const result = await apiRequest('/health');
  
  if (result.ok && result.data.status === 'healthy') {
    console.log('✅ Health check passed');
    return true;
  } else {
    console.log('❌ Health check failed:', result.data || result.error);
    return false;
  }
}

async function testPropertySearch() {
  console.log('🔍 Testing property search...');
  const result = await apiRequest('/properties/search?limit=5');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Property search passed - Found ${result.data.data.totalCount} properties`);
    return true;
  } else {
    console.log('❌ Property search failed:', result.data || result.error);
    return false;
  }
}

async function testPropertyFilter() {
  console.log('🔍 Testing property filter...');
  const result = await apiRequest('/properties/filter?priceRange=1000000-5000000&limit=3');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Property filter passed - Found ${result.data.data.properties.length} properties`);
    return true;
  } else {
    console.log('❌ Property filter failed:', result.data || result.error);
    return false;
  }
}

async function testLocationSearch() {
  console.log('🔍 Testing location search...');
  const result = await apiRequest('/search/location?query=Lagos&limit=3');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Location search passed - Found ${result.data.data.totalCount} properties`);
    return true;
  } else {
    console.log('❌ Location search failed:', result.data || result.error);
    return false;
  }
}

async function testNearbySearch() {
  console.log('🔍 Testing nearby search...');
  // Using Lagos coordinates
  const result = await apiRequest('/search/nearby?latitude=6.5244&longitude=3.3792&radius=10&limit=3');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Nearby search passed - Found ${result.data.data.properties.length} properties`);
    return true;
  } else {
    console.log('❌ Nearby search failed:', result.data || result.error);
    return false;
  }
}

async function testMetadataFilters() {
  console.log('🔍 Testing metadata filters...');
  const result = await apiRequest('/metadata/filters');
  
  if (result.ok && result.data.success && result.data.data.propertyTypes) {
    console.log(`✅ Metadata filters passed - Found ${result.data.data.propertyTypes.length} property types`);
    return true;
  } else {
    console.log('❌ Metadata filters failed:', result.data || result.error);
    return false;
  }
}

async function testMetadataStats() {
  console.log('🔍 Testing metadata stats...');
  const result = await apiRequest('/metadata/stats');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Metadata stats passed - Total properties: ${result.data.data.totalProperties}`);
    return true;
  } else {
    console.log('❌ Metadata stats failed:', result.data || result.error);
    return false;
  }
}

async function testLocationSuggestions() {
  console.log('🔍 Testing location suggestions...');
  const result = await apiRequest('/search/suggestions?query=Lag&limit=5');
  
  if (result.ok && result.data.success) {
    console.log(`✅ Location suggestions passed - Found ${result.data.data.suggestions.length} suggestions`);
    return true;
  } else {
    console.log('❌ Location suggestions failed:', result.data || result.error);
    return false;
  }
}

async function testInvalidApiKey() {
  console.log('🔍 Testing invalid API key...');
  const result = await apiRequest('/health', {
    headers: { 'X-API-Key': 'invalid-key' }
  });
  
  if (!result.ok && result.status === 401) {
    console.log('✅ Invalid API key test passed - Correctly rejected');
    return true;
  } else {
    console.log('❌ Invalid API key test failed - Should have been rejected');
    return false;
  }
}

async function testRateLimit() {
  console.log('🔍 Testing rate limiting (making 5 quick requests)...');
  const promises = Array(5).fill().map(() => apiRequest('/health'));
  const results = await Promise.all(promises);
  
  const successCount = results.filter(r => r.ok).length;
  if (successCount >= 4) {
    console.log(`✅ Rate limiting test passed - ${successCount}/5 requests succeeded`);
    return true;
  } else {
    console.log(`❌ Rate limiting test failed - Only ${successCount}/5 requests succeeded`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting UrbanEdge AI Assistant API Tests\n');
  console.log(`📍 Testing API at: ${API_BASE_URL}`);
  console.log(`🔑 Using API Key: ${API_KEY}\n`);

  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Property Search', fn: testPropertySearch },
    { name: 'Property Filter', fn: testPropertyFilter },
    { name: 'Location Search', fn: testLocationSearch },
    { name: 'Nearby Search', fn: testNearbySearch },
    { name: 'Metadata Filters', fn: testMetadataFilters },
    { name: 'Metadata Stats', fn: testMetadataStats },
    { name: 'Location Suggestions', fn: testLocationSuggestions },
    { name: 'Invalid API Key', fn: testInvalidApiKey },
    { name: 'Rate Limiting', fn: testRateLimit }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} failed with error:`, error.message);
      failed++;
    }
    console.log(''); // Add spacing between tests
  }

  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! The API is ready for JotForm integration.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the API server and database connection.');
  }

  return failed === 0;
}

// Check if API server is running
async function checkServerStatus() {
  console.log('🔍 Checking if API server is running...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      headers: { 'X-API-Key': API_KEY }
    });
    
    if (response.ok) {
      console.log('✅ API server is running\n');
      return true;
    } else {
      console.log('❌ API server responded with error:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Cannot connect to API server. Please ensure it\'s running on port 3001');
    console.log('💡 Start the server with: npm run api\n');
    return false;
  }
}

// Run the tests
async function main() {
  const serverRunning = await checkServerStatus();
  
  if (!serverRunning) {
    process.exit(1);
  }

  const success = await runTests();
  process.exit(success ? 0 : 1);
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}
