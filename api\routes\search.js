import express from 'express';
import { supabase } from '../server.js';

const router = express.Router();

// Helper function to format currency in Nigerian Naira
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price);
};

// Helper function to format property data for AI consumption
const formatPropertyForAI = (property) => {
  return {
    id: property.id,
    title: property.title,
    location: property.location,
    neighborhood: property.neighborhood,
    price: {
      raw: property.price,
      formatted: formatPrice(property.price)
    },
    bedrooms: property.bedrooms,
    bathrooms: property.bathrooms,
    squareFeet: property.square_feet,
    propertyType: property.property_type,
    saleType: property.sale_type,
    description: property.description,
    coordinates: property.latitude && property.longitude ? {
      latitude: parseFloat(property.latitude),
      longitude: parseFloat(property.longitude)
    } : null,
    images: property.images || [],
    features: property.features || [],
    thumbnailUrl: property.thumbnail_url,
    distance: property.distance || null
  };
};

// GET /api/search/location - Search properties by location name
router.get('/location', async (req, res) => {
  try {
    const { 
      query, 
      limit = 20, 
      minPrice, 
      maxPrice, 
      propertyType, 
      saleType 
    } = req.query;

    if (!query) {
      return res.status(400).json({
        error: 'Missing query parameter',
        message: 'Location query is required'
      });
    }

    // Search properties by location using ILIKE for partial matching
    const params = {
      location: query,
      min_price: minPrice ? parseFloat(minPrice) : null,
      max_price: maxPrice ? parseFloat(maxPrice) : null,
      property_type_ids: propertyType ? [propertyType] : null,
      sale_type_id: saleType || null,
      limit_val: parseInt(limit),
      offset_val: 0,
      sort_by: 'newest'
    };

    const { data, error } = await supabase.rpc('filter_properties', params);

    if (error) {
      return res.status(500).json({
        error: 'Location search failed',
        message: error.message
      });
    }

    const formattedProperties = data.properties.map(formatPropertyForAI);

    res.json({
      success: true,
      data: {
        properties: formattedProperties,
        totalCount: data.total_count,
        searchQuery: query,
        suggestions: await getLocationSuggestions(query)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Location search error:', error);
    res.status(500).json({
      error: 'Location search failed',
      message: error.message
    });
  }
});

// GET /api/search/nearby - Find properties near specific coordinates
router.get('/nearby', async (req, res) => {
  try {
    const { 
      latitude, 
      longitude, 
      radius = 5, // Default 5km radius
      limit = 20,
      minPrice,
      maxPrice,
      propertyType,
      saleType
    } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        error: 'Missing coordinates',
        message: 'Both latitude and longitude are required'
      });
    }

    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);
    const radiusKm = parseFloat(radius);

    // Validate coordinates
    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return res.status(400).json({
        error: 'Invalid coordinates',
        message: 'Latitude must be between -90 and 90, longitude between -180 and 180'
      });
    }

    // Use Haversine formula to find nearby properties
    // This is a simplified approach - for production, consider using PostGIS
    const { data: properties, error } = await supabase
      .from('properties')
      .select(`
        *,
        property_types(name),
        sale_types(name),
        property_images(image_url),
        property_features(features(name))
      `)
      .not('latitude', 'is', null)
      .not('longitude', 'is', null);

    if (error) {
      return res.status(500).json({
        error: 'Database query failed',
        message: error.message
      });
    }

    // Calculate distances and filter by radius
    const nearbyProperties = properties
      .map(property => {
        const propLat = parseFloat(property.latitude);
        const propLng = parseFloat(property.longitude);
        
        // Haversine formula for distance calculation
        const R = 6371; // Earth's radius in kilometers
        const dLat = (propLat - lat) * Math.PI / 180;
        const dLng = (propLng - lng) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat * Math.PI / 180) * Math.cos(propLat * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c;

        return {
          ...property,
          distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
          property_type: property.property_types?.name,
          sale_type: property.sale_types?.name,
          images: property.property_images?.map(img => img.image_url) || [],
          features: property.property_features?.map(pf => pf.features?.name).filter(Boolean) || [],
          thumbnail_url: property.property_images?.[0]?.image_url
        };
      })
      .filter(property => property.distance <= radiusKm)
      .filter(property => {
        // Apply additional filters
        if (minPrice && property.price < parseFloat(minPrice)) return false;
        if (maxPrice && property.price > parseFloat(maxPrice)) return false;
        if (propertyType && property.property_type !== propertyType) return false;
        if (saleType && property.sale_type !== saleType) return false;
        return true;
      })
      .sort((a, b) => a.distance - b.distance) // Sort by distance
      .slice(0, parseInt(limit));

    const formattedProperties = nearbyProperties.map(formatPropertyForAI);

    res.json({
      success: true,
      data: {
        properties: formattedProperties,
        totalCount: nearbyProperties.length,
        searchCenter: { latitude: lat, longitude: lng },
        radiusKm: radiusKm,
        averageDistance: nearbyProperties.length > 0 
          ? Math.round((nearbyProperties.reduce((sum, p) => sum + p.distance, 0) / nearbyProperties.length) * 100) / 100
          : 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Nearby search error:', error);
    res.status(500).json({
      error: 'Nearby search failed',
      message: error.message
    });
  }
});

// GET /api/search/suggestions - Get location suggestions for autocomplete
router.get('/suggestions', async (req, res) => {
  try {
    const { query, limit = 10 } = req.query;

    if (!query || query.length < 2) {
      return res.json({
        success: true,
        data: { suggestions: [] }
      });
    }

    const suggestions = await getLocationSuggestions(query, parseInt(limit));

    res.json({
      success: true,
      data: { suggestions },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Suggestions error:', error);
    res.status(500).json({
      error: 'Failed to get suggestions',
      message: error.message
    });
  }
});

// Helper function to get location suggestions
async function getLocationSuggestions(query, limit = 10) {
  try {
    // Get unique locations and neighborhoods from properties
    const { data: locations, error: locError } = await supabase
      .from('properties')
      .select('location')
      .ilike('location', `%${query}%`)
      .limit(limit);

    const { data: neighborhoods, error: neighError } = await supabase
      .from('properties')
      .select('neighborhood')
      .not('neighborhood', 'is', null)
      .ilike('neighborhood', `%${query}%`)
      .limit(limit);

    if (locError || neighError) {
      console.error('Suggestion query error:', locError || neighError);
      return [];
    }

    // Combine and deduplicate suggestions
    const allSuggestions = [
      ...(locations?.map(l => l.location) || []),
      ...(neighborhoods?.map(n => n.neighborhood) || [])
    ];

    const uniqueSuggestions = [...new Set(allSuggestions)]
      .filter(Boolean)
      .slice(0, limit);

    return uniqueSuggestions;

  } catch (error) {
    console.error('Error getting location suggestions:', error);
    return [];
  }
}

export default router;
