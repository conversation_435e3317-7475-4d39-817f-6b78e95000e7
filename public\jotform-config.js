/**
 * JotForm AI Assistant Configuration Script for UrbanEdge Real Estate
 * This script configures the JotForm AI Assistant to work with UrbanEdge property API
 */

// Configuration constants
const URBANEDGE_CONFIG = {
  apiBaseUrl: 'http://localhost:3001/api',
  apiKey: 'urbanedge-ai-assistant-key-2024',
  formId: '01977ef6126a721ca60420e562e7a5468ca6'
};

// Wait for JotForm to be available
function waitForJotForm(callback, maxAttempts = 50) {
  let attempts = 0;
  
  const checkJotForm = () => {
    attempts++;
    
    if (window.JotFormAgent || window.JF) {
      console.log('✅ JotForm detected, configuring UrbanEdge integration...');
      callback();
    } else if (attempts < maxAttempts) {
      setTimeout(checkJotForm, 200);
    } else {
      console.error('❌ JotForm not detected after maximum attempts');
    }
  };
  
  checkJotForm();
}

// Configure JotForm AI Assistant
function configureUrbanEdgeAI() {
  try {
    // Set up API endpoints
    const apiEndpoints = {
      propertySearch: `${URBANEDGE_CONFIG.apiBaseUrl}/properties/search`,
      propertyFilter: `${URBANEDGE_CONFIG.apiBaseUrl}/properties/filter`,
      propertyDetails: `${URBANEDGE_CONFIG.apiBaseUrl}/properties`,
      locationSearch: `${URBANEDGE_CONFIG.apiBaseUrl}/search/location`,
      nearbySearch: `${URBANEDGE_CONFIG.apiBaseUrl}/search/nearby`,
      metadata: `${URBANEDGE_CONFIG.apiBaseUrl}/metadata/filters`,
      suggestions: `${URBANEDGE_CONFIG.apiBaseUrl}/search/suggestions`,
      health: `${URBANEDGE_CONFIG.apiBaseUrl}/health`
    };

    // API request headers
    const apiHeaders = {
      'X-API-Key': URBANEDGE_CONFIG.apiKey,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // AI Agent Instructions
    const agentInstructions = `
You are UrbanEdge AI, a specialized real estate assistant for Nigeria's premier property platform. Your mission is to help customers find their perfect property using our comprehensive database.

## Core Capabilities:
🏠 Search properties by location, price, size, and features
💰 Filter by budget ranges in Nigerian Naira (₦)
📍 Find properties near specific locations
🔍 Provide detailed property information
📊 Explain market trends and pricing

## API Usage Guidelines:

### Property Search Patterns:
- General search: "properties in [location]" → GET /properties/search?location=[location]
- Budget search: "under ₦[amount]" → GET /properties/search?maxPrice=[amount]
- Specific needs: "[bedrooms] bedroom [type] in [location]" → GET /properties/search?location=[location]&bedrooms=[num]&propertyType=[type]

### Location Intelligence:
- City/area queries → GET /search/location?query=[location]
- "Near [landmark]" → GET /search/nearby with coordinates
- Suggestions → GET /search/suggestions?query=[partial_location]

### Price Handling:
- Always use Nigerian Naira (₦)
- Convert millions: "5M" = 5,000,000
- Price ranges: GET /properties/filter?priceRange=[min]-[max]

## Response Format:
1. **Property Listings**: Show title, location, price (₦), bedrooms/bathrooms, key features
2. **No Results**: Suggest broader criteria or alternative locations
3. **Follow-up**: Always ask if they want more details or different search criteria

## Example Interactions:

Customer: "3-bedroom apartments in Lagos under ₦5 million"
Action: GET /properties/search?location=Lagos&bedrooms=3&propertyType=apartment&maxPrice=5000000

Customer: "Houses for rent in Victoria Island"
Action: GET /search/location?query=Victoria Island&propertyType=house&saleType=rent

Customer: "Properties near Lekki Phase 1"
Action: GET /search/location?query=Lekki Phase 1

## Important Rules:
✅ Always use the API for current data
✅ Format prices in Nigerian Naira with proper commas
✅ Be conversational but professional
✅ Suggest viewings and contact information
✅ Handle "no results" gracefully with alternatives
❌ Never invent property details
❌ Don't quote outdated prices or information

When customers ask about properties, ALWAYS make an API call to get the most current information.
`;

    // Configure JotForm Agent
    if (window.JotFormAgent) {
      window.JotFormAgent.configure({
        apiEndpoints: apiEndpoints,
        apiHeaders: apiHeaders,
        agentPrompt: agentInstructions,
        
        // Response templates
        templates: {
          propertyList: `
{if properties && properties.length > 0}
🏠 **Found {properties.length} properties for you:**

{properties.map(p => `
**${p.title}**
📍 ${p.location}${p.neighborhood ? ', ' + p.neighborhood : ''}
💰 ${p.price.formatted}
🛏️ ${p.bedrooms} bed • 🚿 ${p.bathrooms} bath • 📐 ${p.squareFeet} sq ft
🏢 ${p.propertyType} for ${p.saleType}

${p.description ? p.description.substring(0, 120) + '...' : ''}
${p.features && p.features.length > 0 ? '✨ ' + p.features.slice(0, 3).join(', ') : ''}

---
`).join('')}

💡 Would you like more details about any property, or shall I search with different criteria?
{else}
🔍 **No properties found matching your criteria.**

Let me help you find alternatives:
• Try expanding your search area
• Consider adjusting your budget range  
• Look at different property types
• Check both rent and buy options

What would you like to adjust in your search?
{endif}
          `,
          
          error: `
🔧 **I'm having trouble accessing our property database right now.**

Here's what you can do:
• Try your search again in a moment
• Contact our team directly: +234-XXX-XXXX
• Visit our website: urbanedge.com
• Email us: <EMAIL>

Our real estate experts are ready to help you find the perfect property! 🏠
          `
        },

        // Styling
        theme: {
          primaryColor: '#574C3F', // Brown color from UrbanEdge theme
          accentColor: '#B9A590',  // Taupe color
          backgroundColor: '#F6F3EC', // Beige light
          textColor: '#36302A'     // Brown dark
        },

        // Behavior settings
        settings: {
          welcomeMessage: "👋 Hi! I'm UrbanEdge AI, your personal real estate assistant. I can help you find properties across Nigeria. What kind of property are you looking for?",
          placeholder: "Ask me about properties in Lagos, Abuja, or anywhere in Nigeria...",
          maxResults: 10,
          timeout: 15000,
          retryAttempts: 2
        }
      });

      console.log('✅ UrbanEdge AI Assistant configured successfully');
      
      // Test API connection
      testAPIConnection();
      
    } else if (window.JF) {
      // Alternative JotForm configuration method
      console.log('🔄 Using alternative JotForm configuration...');
      configureAlternativeMethod();
    }

  } catch (error) {
    console.error('❌ Error configuring UrbanEdge AI:', error);
  }
}

// Alternative configuration method
function configureAlternativeMethod() {
  // Inject configuration into JotForm's global scope
  window.URBANEDGE_AI_CONFIG = {
    apiBaseUrl: URBANEDGE_CONFIG.apiBaseUrl,
    apiKey: URBANEDGE_CONFIG.apiKey,
    endpoints: {
      search: '/properties/search',
      filter: '/properties/filter',
      location: '/search/location',
      nearby: '/search/nearby'
    }
  };

  console.log('✅ Alternative UrbanEdge configuration injected');
}

// Test API connection
async function testAPIConnection() {
  try {
    const response = await fetch(`${URBANEDGE_CONFIG.apiBaseUrl}/health`, {
      headers: {
        'X-API-Key': URBANEDGE_CONFIG.apiKey,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ UrbanEdge API connection successful:', data.status);
    } else {
      console.warn('⚠️ UrbanEdge API responded with status:', response.status);
    }
  } catch (error) {
    console.error('❌ UrbanEdge API connection failed:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    waitForJotForm(configureUrbanEdgeAI);
  });
} else {
  waitForJotForm(configureUrbanEdgeAI);
}

// Also try when window loads (backup)
window.addEventListener('load', () => {
  setTimeout(() => {
    if (!window.URBANEDGE_AI_CONFIGURED) {
      console.log('🔄 Backup configuration attempt...');
      waitForJotForm(configureUrbanEdgeAI, 20);
    }
  }, 2000);
});

// Export for manual configuration if needed
window.configureUrbanEdgeAI = configureUrbanEdgeAI;
window.URBANEDGE_CONFIG = URBANEDGE_CONFIG;
