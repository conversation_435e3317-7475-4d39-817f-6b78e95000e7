import express from 'express';
import { supabase } from '../server.js';

const router = express.Router();

// GET /api/metadata/filters - Get all available filter options
router.get('/filters', async (req, res) => {
  try {
    // Get all filter options using the existing RPC function
    const { data, error } = await supabase.rpc('get_filter_options');

    if (error) {
      return res.status(500).json({
        error: 'Failed to fetch filter options',
        message: error.message
      });
    }

    // Get price ranges from actual property data
    const { data: priceStats, error: priceError } = await supabase
      .from('properties')
      .select('price')
      .order('price', { ascending: true });

    if (priceError) {
      console.error('Price stats error:', priceError);
    }

    const prices = priceStats?.map(p => p.price) || [];
    const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const maxPrice = prices.length > 0 ? Math.max(...prices) : 10000000;

    // Create price range suggestions
    const priceRanges = [
      { label: 'Under ₦1M', value: '0-1000000', min: 0, max: 1000000 },
      { label: '₦1M - ₦5M', value: '1000000-5000000', min: 1000000, max: 5000000 },
      { label: '₦5M - ₦10M', value: '5000000-10000000', min: 5000000, max: 10000000 },
      { label: '₦10M - ₦20M', value: '10000000-20000000', min: 10000000, max: 20000000 },
      { label: '₦20M - ₦50M', value: '20000000-50000000', min: 20000000, max: 50000000 },
      { label: 'Above ₦50M', value: '50000000-999999999', min: 50000000, max: 999999999 }
    ];

    // Get bedroom and bathroom ranges from actual data
    const { data: roomStats, error: roomError } = await supabase
      .from('properties')
      .select('bedrooms, bathrooms');

    let bedroomRanges = [];
    let bathroomRanges = [];

    if (!roomError && roomStats) {
      const bedrooms = roomStats.map(p => p.bedrooms).filter(Boolean);
      const bathrooms = roomStats.map(p => p.bathrooms).filter(Boolean);

      const maxBedrooms = Math.max(...bedrooms, 5);
      const maxBathrooms = Math.max(...bathrooms, 5);

      bedroomRanges = [
        { label: '1 Bedroom', value: '1-1', min: 1, max: 1 },
        { label: '2 Bedrooms', value: '2-2', min: 2, max: 2 },
        { label: '3 Bedrooms', value: '3-3', min: 3, max: 3 },
        { label: '4+ Bedrooms', value: `4-${maxBedrooms}`, min: 4, max: maxBedrooms }
      ];

      bathroomRanges = [
        { label: '1 Bathroom', value: '1-1', min: 1, max: 1 },
        { label: '2 Bathrooms', value: '2-2', min: 2, max: 2 },
        { label: '3+ Bathrooms', value: `3-${maxBathrooms}`, min: 3, max: maxBathrooms }
      ];
    }

    res.json({
      success: true,
      data: {
        propertyTypes: data.property_types || [],
        saleTypes: data.sale_types || [],
        features: data.features || [],
        priceRanges,
        bedroomRanges,
        bathroomRanges,
        priceStats: {
          min: minPrice,
          max: maxPrice,
          average: prices.length > 0 ? Math.round(prices.reduce((a, b) => a + b, 0) / prices.length) : 0
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({
      error: 'Failed to fetch metadata',
      message: error.message
    });
  }
});

// GET /api/metadata/locations - Get all available locations and neighborhoods
router.get('/locations', async (req, res) => {
  try {
    const { search } = req.query;

    let query = supabase
      .from('properties')
      .select('location, neighborhood')
      .not('location', 'is', null);

    if (search) {
      query = query.or(`location.ilike.%${search}%,neighborhood.ilike.%${search}%`);
    }

    const { data, error } = await query.limit(100);

    if (error) {
      return res.status(500).json({
        error: 'Failed to fetch locations',
        message: error.message
      });
    }

    // Extract unique locations and neighborhoods
    const locations = [...new Set(data.map(p => p.location).filter(Boolean))].sort();
    const neighborhoods = [...new Set(data.map(p => p.neighborhood).filter(Boolean))].sort();

    res.json({
      success: true,
      data: {
        locations,
        neighborhoods,
        combined: [...new Set([...locations, ...neighborhoods])].sort()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Locations error:', error);
    res.status(500).json({
      error: 'Failed to fetch locations',
      message: error.message
    });
  }
});

// GET /api/metadata/stats - Get property statistics for AI context
router.get('/stats', async (req, res) => {
  try {
    // Get comprehensive property statistics
    const { data: properties, error } = await supabase
      .from('properties')
      .select(`
        price,
        bedrooms,
        bathrooms,
        square_feet,
        location,
        neighborhood,
        property_types(name),
        sale_types(name)
      `);

    if (error) {
      return res.status(500).json({
        error: 'Failed to fetch statistics',
        message: error.message
      });
    }

    if (!properties || properties.length === 0) {
      return res.json({
        success: true,
        data: {
          totalProperties: 0,
          message: 'No properties available'
        }
      });
    }

    // Calculate statistics
    const prices = properties.map(p => p.price).filter(Boolean);
    const bedrooms = properties.map(p => p.bedrooms).filter(Boolean);
    const bathrooms = properties.map(p => p.bathrooms).filter(Boolean);
    const squareFeet = properties.map(p => p.square_feet).filter(Boolean);

    // Property type distribution
    const propertyTypeStats = properties.reduce((acc, p) => {
      const type = p.property_types?.name || 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    // Sale type distribution
    const saleTypeStats = properties.reduce((acc, p) => {
      const type = p.sale_types?.name || 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    // Location distribution (top 10)
    const locationStats = properties.reduce((acc, p) => {
      const location = p.location || 'Unknown';
      acc[location] = (acc[location] || 0) + 1;
      return acc;
    }, {});

    const topLocations = Object.entries(locationStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .reduce((acc, [location, count]) => {
        acc[location] = count;
        return acc;
      }, {});

    res.json({
      success: true,
      data: {
        totalProperties: properties.length,
        priceStats: {
          min: Math.min(...prices),
          max: Math.max(...prices),
          average: Math.round(prices.reduce((a, b) => a + b, 0) / prices.length),
          median: prices.sort((a, b) => a - b)[Math.floor(prices.length / 2)]
        },
        bedroomStats: {
          min: Math.min(...bedrooms),
          max: Math.max(...bedrooms),
          average: Math.round((bedrooms.reduce((a, b) => a + b, 0) / bedrooms.length) * 10) / 10
        },
        bathroomStats: {
          min: Math.min(...bathrooms),
          max: Math.max(...bathrooms),
          average: Math.round((bathrooms.reduce((a, b) => a + b, 0) / bathrooms.length) * 10) / 10
        },
        squareFeetStats: squareFeet.length > 0 ? {
          min: Math.min(...squareFeet),
          max: Math.max(...squareFeet),
          average: Math.round(squareFeet.reduce((a, b) => a + b, 0) / squareFeet.length)
        } : null,
        propertyTypeDistribution: propertyTypeStats,
        saleTypeDistribution: saleTypeStats,
        topLocations
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

export default router;
