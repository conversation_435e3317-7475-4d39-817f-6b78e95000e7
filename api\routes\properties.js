import express from 'express';
import { supabase } from '../server.js';

const router = express.Router();

// Helper function to format currency in Nigerian Naira
const formatPrice = (price) => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price);
};

// Helper function to format property data for AI consumption
const formatPropertyForAI = (property) => {
  return {
    id: property.id,
    title: property.title,
    location: property.location,
    neighborhood: property.neighborhood,
    price: {
      raw: property.price,
      formatted: formatPrice(property.price)
    },
    bedrooms: property.bedrooms,
    bathrooms: property.bathrooms,
    squareFeet: property.square_feet,
    propertyType: property.property_type,
    saleType: property.sale_type,
    description: property.description,
    coordinates: property.latitude && property.longitude ? {
      latitude: parseFloat(property.latitude),
      longitude: parseFloat(property.longitude)
    } : null,
    images: property.images || [],
    features: property.features || [],
    floorPlanUrl: property.floor_plan_url,
    createdAt: property.created_at,
    thumbnailUrl: property.thumbnail_url
  };
};

// GET /api/properties/search - Search properties with various filters
router.get('/search', async (req, res) => {
  try {
    const {
      location,
      minPrice,
      maxPrice,
      minBedrooms,
      maxBedrooms,
      minBathrooms,
      maxBathrooms,
      propertyType,
      saleType,
      features,
      limit = 20,
      offset = 0,
      sortBy = 'newest'
    } = req.query;

    // Parse features if provided
    let featureIds = null;
    if (features) {
      featureIds = Array.isArray(features) ? features : features.split(',');
    }

    // Parse property type
    let propertyTypeIds = null;
    if (propertyType) {
      propertyTypeIds = Array.isArray(propertyType) ? propertyType : [propertyType];
    }

    const params = {
      location: location || null,
      min_price: minPrice ? parseFloat(minPrice) : null,
      max_price: maxPrice ? parseFloat(maxPrice) : null,
      min_bedrooms: minBedrooms ? parseInt(minBedrooms) : null,
      max_bedrooms: maxBedrooms ? parseInt(maxBedrooms) : null,
      min_bathrooms: minBathrooms ? parseInt(minBathrooms) : null,
      max_bathrooms: maxBathrooms ? parseInt(maxBathrooms) : null,
      property_type_ids: propertyTypeIds,
      feature_ids: featureIds,
      sale_type_id: saleType || null,
      limit_val: parseInt(limit),
      offset_val: parseInt(offset),
      sort_by: sortBy
    };

    const { data, error } = await supabase.rpc('filter_properties', params);

    if (error) {
      console.error('Database error:', error);
      return res.status(500).json({
        error: 'Database query failed',
        message: error.message
      });
    }

    // Format properties for AI consumption
    const formattedProperties = data.properties.map(formatPropertyForAI);

    res.json({
      success: true,
      data: {
        properties: formattedProperties,
        totalCount: data.total_count,
        limit: data.limit,
        offset: data.offset,
        hasMore: data.offset + data.limit < data.total_count
      },
      query: params,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      error: 'Search failed',
      message: error.message
    });
  }
});

// GET /api/properties/filter - Advanced filtering with price ranges
router.get('/filter', async (req, res) => {
  try {
    const {
      priceRange,
      bedroomRange,
      bathroomRange,
      location,
      propertyTypes,
      features,
      saleType,
      sortBy = 'price_asc'
    } = req.query;

    let minPrice = null, maxPrice = null;
    let minBedrooms = null, maxBedrooms = null;
    let minBathrooms = null, maxBathrooms = null;

    // Parse price range (e.g., "500000-2000000")
    if (priceRange && priceRange !== 'any') {
      const [min, max] = priceRange.split('-').map(p => parseFloat(p));
      minPrice = min || null;
      maxPrice = max || null;
    }

    // Parse bedroom range (e.g., "2-4")
    if (bedroomRange && bedroomRange !== 'any') {
      const [min, max] = bedroomRange.split('-').map(b => parseInt(b));
      minBedrooms = min || null;
      maxBedrooms = max || null;
    }

    // Parse bathroom range (e.g., "1-3")
    if (bathroomRange && bathroomRange !== 'any') {
      const [min, max] = bathroomRange.split('-').map(b => parseInt(b));
      minBathrooms = min || null;
      maxBathrooms = max || null;
    }

    const params = {
      location: location || null,
      min_price: minPrice,
      max_price: maxPrice,
      min_bedrooms: minBedrooms,
      max_bedrooms: maxBedrooms,
      min_bathrooms: minBathrooms,
      max_bathrooms: maxBathrooms,
      property_type_ids: propertyTypes ? propertyTypes.split(',') : null,
      feature_ids: features ? features.split(',') : null,
      sale_type_id: saleType || null,
      limit_val: 50,
      offset_val: 0,
      sort_by: sortBy
    };

    const { data, error } = await supabase.rpc('filter_properties', params);

    if (error) {
      return res.status(500).json({
        error: 'Filter query failed',
        message: error.message
      });
    }

    const formattedProperties = data.properties.map(formatPropertyForAI);

    res.json({
      success: true,
      data: {
        properties: formattedProperties,
        totalCount: data.total_count,
        appliedFilters: {
          priceRange: priceRange || 'any',
          bedroomRange: bedroomRange || 'any',
          bathroomRange: bathroomRange || 'any',
          location: location || 'any',
          propertyTypes: propertyTypes || 'any',
          features: features || 'none',
          saleType: saleType || 'any'
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Filter error:', error);
    res.status(500).json({
      error: 'Filtering failed',
      message: error.message
    });
  }
});

// GET /api/properties/:id - Get detailed property information
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        error: 'Invalid property ID',
        message: 'Property ID must be a valid UUID'
      });
    }

    const { data, error } = await supabase.rpc('get_property_details', {
      property_id: id
    });

    if (error) {
      return res.status(500).json({
        error: 'Database query failed',
        message: error.message
      });
    }

    if (!data) {
      return res.status(404).json({
        error: 'Property not found',
        message: `No property found with ID: ${id}`
      });
    }

    const formattedProperty = formatPropertyForAI(data);

    res.json({
      success: true,
      data: formattedProperty,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Property detail error:', error);
    res.status(500).json({
      error: 'Failed to fetch property details',
      message: error.message
    });
  }
});

export default router;
