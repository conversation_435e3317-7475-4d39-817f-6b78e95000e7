import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import Navbar from "./Navbar";
import Footer from "./Footer";
import JotFormAssistant from "../AI/JotFormAssistant";

const Layout = ({ children }) => {
  const location = useLocation();
  const [showAIAssistant, setShowAIAssistant] = useState(true);

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  // Determine if AI Assistant should be shown on current page
  const shouldShowAIAssistant = () => {
    const publicPages = ['/', '/properties', '/services', '/about', '/contact'];
    const currentPath = location.pathname;

    // Show on public pages and property-related pages
    return publicPages.includes(currentPath) ||
           currentPath.startsWith('/properties/') ||
           currentPath.startsWith('/client/');
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow pt-16">{children}</main>
      <Footer />

      {/* JotForm AI Assistant */}
      {shouldShowAIAssistant() && (
        <JotFormAssistant
          isVisible={showAIAssistant}
          onToggle={setShowAIAssistant}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default Layout;
