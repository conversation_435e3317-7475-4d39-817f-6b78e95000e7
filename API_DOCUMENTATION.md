# UrbanEdge AI Assistant API Documentation

## Overview

The UrbanEdge AI Assistant API provides secure access to property data for external services like JotForm AI assistants. This API enables location-based searches, price filtering, and comprehensive property information retrieval.

## Base URL

- **Development**: `http://localhost:3001/api`
- **Production**: `https://your-domain.com/api`

## Authentication

All API requests require authentication using an API key. Include the API key in the request header:

```
X-API-Key: your-api-key-here
```

Or as a query parameter:
```
?api_key=your-api-key-here
```

### Default API Key (for initial setup)
```
urbanedge-ai-assistant-key-2024
```

**⚠️ Important**: Generate new API keys for production use through the admin dashboard.

## Rate Limiting

- **Default**: 100 requests per 15-minute window per IP
- **Configurable**: Rate limits can be adjusted per API key

## Response Format

All responses follow this structure:

```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

Error responses:
```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Endpoints

### 1. Health Check

**GET** `/api/health`

Check API status and connectivity.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "service": "UrbanEdge AI Assistant API"
}
```

### 2. Property Search

**GET** `/api/properties/search`

Search properties with various filters.

**Query Parameters:**
- `location` (string): Location or neighborhood name
- `minPrice` (number): Minimum price in Naira
- `maxPrice` (number): Maximum price in Naira
- `minBedrooms` (number): Minimum number of bedrooms
- `maxBedrooms` (number): Maximum number of bedrooms
- `minBathrooms` (number): Minimum number of bathrooms
- `maxBathrooms` (number): Maximum number of bathrooms
- `propertyType` (string): Property type ID
- `saleType` (string): Sale type ID (rent/buy)
- `features` (array): Feature IDs (comma-separated)
- `limit` (number): Results per page (max 100, default 20)
- `offset` (number): Pagination offset (default 0)
- `sortBy` (string): Sort order (newest, oldest, price_asc, price_desc, bedrooms_asc, bedrooms_desc)

**Example Request:**
```
GET /api/properties/search?location=Lagos&minPrice=1000000&maxPrice=5000000&bedrooms=3&saleType=buy&limit=10
```

**Response:**
```json
{
  "success": true,
  "data": {
    "properties": [
      {
        "id": "uuid",
        "title": "Luxury 3-Bedroom Apartment",
        "location": "Victoria Island, Lagos",
        "neighborhood": "Victoria Island",
        "price": {
          "raw": 3500000,
          "formatted": "₦3,500,000"
        },
        "bedrooms": 3,
        "bathrooms": 2,
        "squareFeet": 1200,
        "propertyType": "apartment",
        "saleType": "buy",
        "description": "Modern apartment with ocean view...",
        "coordinates": {
          "latitude": 6.4281,
          "longitude": 3.4219
        },
        "images": ["url1", "url2"],
        "features": ["swimming_pool", "gym", "security"],
        "thumbnailUrl": "image_url",
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "totalCount": 45,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 3. Advanced Property Filtering

**GET** `/api/properties/filter`

Advanced filtering with predefined ranges.

**Query Parameters:**
- `priceRange` (string): Price range (e.g., "1000000-5000000", "any")
- `bedroomRange` (string): Bedroom range (e.g., "2-4", "any")
- `bathroomRange` (string): Bathroom range (e.g., "1-3", "any")
- `location` (string): Location filter
- `propertyTypes` (string): Comma-separated property type IDs
- `features` (string): Comma-separated feature IDs
- `saleType` (string): Sale type ID
- `sortBy` (string): Sort order

**Example Request:**
```
GET /api/properties/filter?priceRange=1000000-5000000&bedroomRange=2-4&location=Lagos&propertyTypes=apartment,house
```

### 4. Property Details

**GET** `/api/properties/:id`

Get detailed information about a specific property.

**Parameters:**
- `id` (UUID): Property ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "Luxury 3-Bedroom Apartment",
    "location": "Victoria Island, Lagos",
    "neighborhood": "Victoria Island",
    "price": {
      "raw": 3500000,
      "formatted": "₦3,500,000"
    },
    "bedrooms": 3,
    "bathrooms": 2,
    "squareFeet": 1200,
    "propertyType": "apartment",
    "saleType": "buy",
    "description": "Detailed property description...",
    "coordinates": {
      "latitude": 6.4281,
      "longitude": 3.4219
    },
    "images": [
      {
        "url": "image_url",
        "order": 1
      }
    ],
    "features": [
      {
        "id": "uuid",
        "name": "swimming_pool"
      }
    ],
    "floorPlanUrl": "floor_plan_url",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 5. Location-Based Search

**GET** `/api/search/location`

Search properties by location name with fuzzy matching.

**Query Parameters:**
- `query` (string, required): Location search query
- `limit` (number): Results limit (default 20)
- `minPrice` (number): Minimum price filter
- `maxPrice` (number): Maximum price filter
- `propertyType` (string): Property type filter
- `saleType` (string): Sale type filter

**Response:** Similar to property search with additional location suggestions.

### 6. Nearby Properties

**GET** `/api/search/nearby`

Find properties near specific coordinates.

**Query Parameters:**
- `latitude` (number, required): Latitude coordinate
- `longitude` (number, required): Longitude coordinate
- `radius` (number): Search radius in kilometers (default 5)
- `limit` (number): Results limit (default 20)
- `minPrice` (number): Minimum price filter
- `maxPrice` (number): Maximum price filter
- `propertyType` (string): Property type filter
- `saleType` (string): Sale type filter

**Example Request:**
```
GET /api/search/nearby?latitude=6.4281&longitude=3.4219&radius=10&limit=15
```

### 7. Filter Metadata

**GET** `/api/metadata/filters`

Get all available filter options for building search interfaces.

**Response:**
```json
{
  "success": true,
  "data": {
    "propertyTypes": [
      {"id": "uuid", "name": "apartment"},
      {"id": "uuid", "name": "house"}
    ],
    "saleTypes": [
      {"id": "uuid", "name": "rent"},
      {"id": "uuid", "name": "buy"}
    ],
    "features": [
      {"id": "uuid", "name": "swimming_pool"},
      {"id": "uuid", "name": "gym"}
    ],
    "priceRanges": [
      {"label": "Under ₦1M", "value": "0-1000000", "min": 0, "max": 1000000},
      {"label": "₦1M - ₦5M", "value": "1000000-5000000", "min": 1000000, "max": 5000000}
    ],
    "bedroomRanges": [
      {"label": "1 Bedroom", "value": "1-1", "min": 1, "max": 1},
      {"label": "2 Bedrooms", "value": "2-2", "min": 2, "max": 2}
    ],
    "bathroomRanges": [
      {"label": "1 Bathroom", "value": "1-1", "min": 1, "max": 1}
    ],
    "priceStats": {
      "min": 500000,
      "max": 50000000,
      "average": 5500000
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 8. Location Suggestions

**GET** `/api/search/suggestions`

Get location autocomplete suggestions.

**Query Parameters:**
- `query` (string): Search query (minimum 2 characters)
- `limit` (number): Maximum suggestions (default 10)

### 9. Property Statistics

**GET** `/api/metadata/stats`

Get comprehensive property statistics for AI context.

**Response:** Includes total properties, price statistics, property type distribution, and top locations.

## Error Codes

- **400**: Bad Request - Invalid parameters
- **401**: Unauthorized - Invalid or missing API key
- **404**: Not Found - Resource not found
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error - Server error

## Usage Examples for AI Assistants

### Common Queries

1. **"Show me 3-bedroom apartments in Lagos under ₦5M"**
   ```
   GET /api/properties/search?location=Lagos&bedrooms=3&propertyType=apartment&maxPrice=5000000
   ```

2. **"Find houses for rent near Victoria Island"**
   ```
   GET /api/search/location?query=Victoria Island&propertyType=house&saleType=rent
   ```

3. **"What properties are available within 10km of these coordinates?"**
   ```
   GET /api/search/nearby?latitude=6.4281&longitude=3.4219&radius=10
   ```

## Security Considerations

- All API keys are hashed and stored securely
- Rate limiting prevents abuse
- Read-only access for external services
- CORS configured for specific domains
- Request logging for monitoring

## JotForm Integration Guide

### Setting Up the AI Assistant in JotForm

1. **Create a new JotForm with AI Assistant**
2. **Configure the Send API Request action:**
   - **Method**: GET
   - **Endpoint**: `https://your-domain.com/api/properties/search`
   - **Headers**:
     ```
     X-API-Key: urbanedge-ai-assistant-key-2024
     Content-Type: application/json
     ```

3. **Sample Agent Prompt for JotForm:**
   ```
   You are a helpful real estate assistant for UrbanEdge Properties in Nigeria.

   When customers ask about properties, use the API to search our database:
   - For location searches: Use /api/properties/search with location parameter
   - For price ranges: Use minPrice and maxPrice parameters
   - For specific requirements: Use bedrooms, bathrooms, propertyType parameters

   Always format prices in Nigerian Naira (₦) and provide helpful, accurate information about available properties.

   Example API calls:
   - "3-bedroom apartments in Lagos": GET /api/properties/search?location=Lagos&bedrooms=3&propertyType=apartment
   - "Houses under ₦5M": GET /api/properties/search?maxPrice=5000000&propertyType=house

   When external data is required, make an API request to get current property information.
   ```

4. **API Execution Message Template:**
   ```
   Found {properties.length} properties matching your criteria:

   {properties.map(p => `
   🏠 ${p.title}
   📍 ${p.location}
   💰 ${p.price.formatted}
   🛏️ ${p.bedrooms} bedrooms, 🚿 ${p.bathrooms} bathrooms
   📐 ${p.squareFeet} sq ft

   ${p.description}
   `).join('\n')}

   Would you like more details about any of these properties or search with different criteria?
   ```

### Testing the Integration

Use these test queries in your JotForm:
- "Show me apartments in Lagos"
- "I need a 3-bedroom house under ₦10 million"
- "What properties do you have in Victoria Island?"
- "Find me houses for rent in Abuja"

## Support

For API support and new API key generation, contact the UrbanEdge development team.
