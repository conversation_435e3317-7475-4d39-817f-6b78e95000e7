# UrbanEdge AI Assistant API Setup Guide

## Overview

This guide will help you set up the AI virtual assistant integration for your UrbanEdge real estate application. The AI assistant can be embedded via JotForm and will have access to your Supabase database to answer customer queries about properties.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Run Setup Script

```bash
npm run setup:api
```

### 3. Update Environment Variables

Edit your `.env` file with your actual Supabase credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_actual_supabase_url
VITE_SUPABASE_ANON_KEY=your_actual_anon_key
VITE_SUPABASE_SERVICE_ROLE_SECRET=your_actual_service_role_key

# API Configuration
JOTFORM_API_KEY=urbanedge-ai-assistant-key-2024
NODE_ENV=development
PORT=3001
```

### 4. Run Database Migrations

```bash
npx supabase db push
```

### 5. Start the API Server

```bash
# Start API server only
npm run api

# Or start both API and frontend
npm run dev:full
```

### 6. Test the API

```bash
npm run test:api
```

## 📋 What's Included

### API Endpoints

- **Health Check**: `GET /api/health`
- **Property Search**: `GET /api/properties/search`
- **Property Filter**: `GET /api/properties/filter`
- **Property Details**: `GET /api/properties/:id`
- **Location Search**: `GET /api/search/location`
- **Nearby Properties**: `GET /api/search/nearby`
- **Filter Metadata**: `GET /api/metadata/filters`
- **Property Statistics**: `GET /api/metadata/stats`
- **Location Suggestions**: `GET /api/search/suggestions`

### Security Features

- ✅ API Key authentication
- ✅ Rate limiting (100 requests per 15 minutes)
- ✅ CORS configuration for JotForm
- ✅ Row Level Security (RLS) policies
- ✅ Request logging and monitoring
- ✅ Secure API key management

### Database Enhancements

- ✅ API keys table for external access management
- ✅ Enhanced RLS policies for service role access
- ✅ Optimized functions for external API calls
- ✅ Geospatial queries for location-based searches

## 🔧 Configuration

### API Keys

The system includes a default API key for initial setup:
```
urbanedge-ai-assistant-key-2024
```

**⚠️ Important**: Generate new API keys for production use through the admin dashboard.

### Admin Dashboard

Access the API key management interface at:
```
http://localhost:5173/admin/dashboard
```

Navigate to the "API Keys" tab to:
- Generate new API keys
- Set permissions and rate limits
- Monitor usage statistics
- Revoke keys when needed

## 🔗 JotForm Integration

### Step 1: Create JotForm with AI Assistant

1. Log into your JotForm account
2. Create a new form with AI Assistant widget
3. Configure the "Send API Request" action

### Step 2: Configure API Request

**Method**: GET
**Endpoint**: `https://your-domain.com/api/properties/search`
**Headers**:
```
X-API-Key: your-api-key-here
Content-Type: application/json
```

### Step 3: Set Up Agent Prompt

```
You are a helpful real estate assistant for UrbanEdge Properties in Nigeria.

When customers ask about properties, use the API to search our database:
- For location searches: Use /api/properties/search with location parameter
- For price ranges: Use minPrice and maxPrice parameters  
- For specific requirements: Use bedrooms, bathrooms, propertyType parameters

Always format prices in Nigerian Naira (₦) and provide helpful, accurate information.

Example API calls:
- "3-bedroom apartments in Lagos": GET /api/properties/search?location=Lagos&bedrooms=3&propertyType=apartment
- "Houses under ₦5M": GET /api/properties/search?maxPrice=5000000&propertyType=house

When external data is required, make an API request to get current property information.
```

### Step 4: Configure Response Template

```
Found {properties.length} properties matching your criteria:

{properties.map(p => `
🏠 ${p.title}
📍 ${p.location}
💰 ${p.price.formatted}
🛏️ ${p.bedrooms} bedrooms, 🚿 ${p.bathrooms} bathrooms
📐 ${p.squareFeet} sq ft

${p.description}
`).join('\n')}

Would you like more details about any of these properties or search with different criteria?
```

## 🧪 Testing

### Automated Tests

Run the comprehensive test suite:
```bash
npm run test:api
```

### Manual Testing

Test individual endpoints:
```bash
# Health check
curl -H "X-API-Key: urbanedge-ai-assistant-key-2024" http://localhost:3001/api/health

# Property search
curl -H "X-API-Key: urbanedge-ai-assistant-key-2024" "http://localhost:3001/api/properties/search?location=Lagos&limit=5"

# Price filter
curl -H "X-API-Key: urbanedge-ai-assistant-key-2024" "http://localhost:3001/api/properties/filter?priceRange=1000000-5000000"
```

### Test Queries for JotForm

Use these sample queries to test your JotForm integration:

1. "Show me apartments in Lagos"
2. "I need a 3-bedroom house under ₦10 million"
3. "What properties do you have in Victoria Island?"
4. "Find me houses for rent in Abuja"
5. "Show me properties with swimming pools"

## 🔒 Security Best Practices

### Production Deployment

1. **Generate New API Keys**: Replace the default key with production keys
2. **Environment Variables**: Use secure environment variable management
3. **HTTPS**: Ensure all API calls use HTTPS in production
4. **Rate Limiting**: Monitor and adjust rate limits based on usage
5. **Logging**: Set up proper logging and monitoring

### API Key Management

- Generate separate keys for different services
- Set appropriate permissions for each key
- Monitor usage and revoke unused keys
- Set expiration dates for temporary access

## 📊 Monitoring

### Available Metrics

- API request counts
- Response times
- Error rates
- Rate limit violations
- Popular search queries

### Logs Location

- API logs: `logs/api.log`
- Error logs: `logs/error.log`

## 🚨 Troubleshooting

### Common Issues

**API Server Won't Start**
- Check if port 3001 is available
- Verify Supabase credentials in `.env`
- Ensure all dependencies are installed

**Database Connection Errors**
- Verify Supabase URL and service role key
- Check if migrations have been applied
- Ensure RLS policies are properly configured

**JotForm Integration Issues**
- Verify API key is correct
- Check CORS configuration
- Ensure endpoint URLs are correct
- Test API endpoints manually first

### Getting Help

1. Check the API documentation: `API_DOCUMENTATION.md`
2. Run the test suite: `npm run test:api`
3. Check server logs for errors
4. Verify database connectivity

## 📈 Performance Optimization

### Caching

The API includes built-in caching for:
- Filter metadata
- Property statistics
- Location suggestions

### Database Optimization

- Indexes on frequently queried fields
- Optimized RPC functions
- Efficient geospatial queries

## 🔄 Updates and Maintenance

### Regular Tasks

1. Monitor API usage and performance
2. Update API keys periodically
3. Review and update rate limits
4. Check for security updates

### Scaling Considerations

- Monitor request volumes
- Consider implementing Redis for caching
- Set up load balancing for high traffic
- Implement database read replicas if needed

## 📞 Support

For technical support or questions about the AI assistant integration, please contact the UrbanEdge development team.
