import express from 'express';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize Supabase client with service role for API access
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_SECRET;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Security middleware
app.use(helmet());

// CORS configuration for JotForm and other external services
app.use(cors({
  origin: [
    'https://www.jotform.com',
    'https://jotform.com',
    'https://eu-api.jotform.com',
    'https://api.jotform.com',
    'http://localhost:5173', // Local development
    'http://localhost:3000', // Local development
  ],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API Key authentication middleware
const authenticateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'] || req.query.api_key;
  
  // For development, allow requests without API key from localhost
  if (process.env.NODE_ENV === 'development' && 
      (req.ip === '127.0.0.1' || req.ip === '::1' || req.hostname === 'localhost')) {
    return next();
  }
  
  // In production, require valid API key
  const validApiKeys = [
    process.env.JOTFORM_API_KEY,
    process.env.EXTERNAL_API_KEY,
    'urbanedge-ai-assistant-key-2024' // Default key for initial setup
  ].filter(Boolean);
  
  if (!apiKey || !validApiKeys.includes(apiKey)) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Valid API key required'
    });
  }
  
  next();
};

// Apply API key authentication to all API routes
app.use('/api', authenticateApiKey);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'UrbanEdge AI Assistant API'
  });
});

// Import route handlers
import propertyRoutes from './routes/properties.js';
import searchRoutes from './routes/search.js';
import metadataRoutes from './routes/metadata.js';

// Register routes
app.use('/api/properties', propertyRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/metadata', metadataRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('API Error:', err);
  
  res.status(err.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    availableEndpoints: [
      'GET /api/health',
      'GET /api/properties/search',
      'GET /api/properties/filter',
      'GET /api/properties/:id',
      'GET /api/search/location',
      'GET /api/search/nearby',
      'GET /api/metadata/filters'
    ]
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 UrbanEdge AI Assistant API running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔑 API Key authentication: ${process.env.NODE_ENV === 'development' ? 'Disabled for localhost' : 'Enabled'}`);
});

export { supabase };
