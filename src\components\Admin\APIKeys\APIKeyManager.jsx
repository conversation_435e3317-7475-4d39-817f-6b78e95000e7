import { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';
import { 
  PlusIcon, 
  KeyIcon, 
  TrashIcon, 
  EyeIcon, 
  EyeSlashIcon,
  ClipboardDocumentIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const APIKeyManager = () => {
  const [apiKeys, setApiKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewKeyForm, setShowNewKeyForm] = useState(false);
  const [newKeyData, setNewKeyData] = useState({
    name: '',
    permissions: { read: true, write: false, search: true, metadata: true },
    rateLimit: 100,
    expiresInDays: null
  });
  const [generatedKey, setGeneratedKey] = useState(null);
  const [visibleKeys, setVisibleKeys] = useState(new Set());
  const [copiedKeys, setCopiedKeys] = useState(new Set());

  useEffect(() => {
    fetchApiKeys();
  }, []);

  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.rpc('list_api_keys');
      
      if (error) throw error;
      setApiKeys(data || []);
    } catch (error) {
      console.error('Error fetching API keys:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateApiKey = async () => {
    try {
      const { data, error } = await supabase.rpc('generate_api_key', {
        key_name: newKeyData.name,
        permissions_obj: newKeyData.permissions,
        rate_limit_val: newKeyData.rateLimit,
        expires_in_days: newKeyData.expiresInDays
      });

      if (error) throw error;
      
      setGeneratedKey(data);
      setShowNewKeyForm(false);
      setNewKeyData({
        name: '',
        permissions: { read: true, write: false, search: true, metadata: true },
        rateLimit: 100,
        expiresInDays: null
      });
      
      await fetchApiKeys();
    } catch (error) {
      console.error('Error generating API key:', error);
      alert('Failed to generate API key: ' + error.message);
    }
  };

  const revokeApiKey = async (keyId) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return;
    }

    try {
      const { data, error } = await supabase.rpc('revoke_api_key', {
        key_id: keyId
      });

      if (error) throw error;
      
      if (data.success) {
        await fetchApiKeys();
      } else {
        alert('Failed to revoke API key: ' + data.error);
      }
    } catch (error) {
      console.error('Error revoking API key:', error);
      alert('Failed to revoke API key: ' + error.message);
    }
  };

  const toggleKeyVisibility = (keyId) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = async (text, keyId) => {
    try {
      await navigator.clipboard.writeText(text);
      const newCopied = new Set(copiedKeys);
      newCopied.add(keyId);
      setCopiedKeys(newCopied);
      
      setTimeout(() => {
        setCopiedKeys(prev => {
          const updated = new Set(prev);
          updated.delete(keyId);
          return updated;
        });
      }, 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-brown dark:text-beige-light">API Key Management</h2>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Manage API keys for external services like JotForm AI assistants
          </p>
        </div>
        <button
          onClick={() => setShowNewKeyForm(true)}
          className="flex items-center gap-2 bg-brown text-white px-4 py-2 rounded-lg hover:bg-brown-dark transition-colors"
        >
          <PlusIcon className="h-5 w-5" />
          Generate New Key
        </button>
      </div>

      {/* Generated Key Display */}
      {generatedKey && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CheckIcon className="h-5 w-5 text-green-600" />
            <h3 className="font-semibold text-green-800 dark:text-green-200">API Key Generated Successfully</h3>
          </div>
          <p className="text-sm text-green-700 dark:text-green-300 mb-3">
            {generatedKey.warning}
          </p>
          <div className="bg-white dark:bg-gray-800 rounded border p-3 font-mono text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-800 dark:text-gray-200">{generatedKey.api_key}</span>
              <button
                onClick={() => copyToClipboard(generatedKey.api_key, 'generated')}
                className="text-green-600 hover:text-green-700 ml-2"
              >
                {copiedKeys.has('generated') ? (
                  <CheckIcon className="h-4 w-4" />
                ) : (
                  <ClipboardDocumentIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
          <button
            onClick={() => setGeneratedKey(null)}
            className="mt-3 text-sm text-green-600 hover:text-green-700"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* New Key Form */}
      {showNewKeyForm && (
        <div className="bg-white dark:bg-brown-dark rounded-lg border border-gray-200 dark:border-brown p-6">
          <h3 className="text-lg font-semibold text-brown dark:text-beige-light mb-4">Generate New API Key</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Key Name
              </label>
              <input
                type="text"
                value={newKeyData.name}
                onChange={(e) => setNewKeyData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., JotForm AI Assistant"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brown focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Permissions
              </label>
              <div className="space-y-2">
                {Object.entries(newKeyData.permissions).map(([permission, enabled]) => (
                  <label key={permission} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={enabled}
                      onChange={(e) => setNewKeyData(prev => ({
                        ...prev,
                        permissions: { ...prev.permissions, [permission]: e.target.checked }
                      }))}
                      className="rounded border-gray-300 text-brown focus:ring-brown"
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize">
                      {permission}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Rate Limit (requests per 15 minutes)
              </label>
              <input
                type="number"
                value={newKeyData.rateLimit}
                onChange={(e) => setNewKeyData(prev => ({ ...prev, rateLimit: parseInt(e.target.value) }))}
                min="1"
                max="1000"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brown focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Expires In (days, optional)
              </label>
              <input
                type="number"
                value={newKeyData.expiresInDays || ''}
                onChange={(e) => setNewKeyData(prev => ({ 
                  ...prev, 
                  expiresInDays: e.target.value ? parseInt(e.target.value) : null 
                }))}
                placeholder="Leave empty for no expiration"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brown focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <button
              onClick={generateApiKey}
              disabled={!newKeyData.name.trim()}
              className="bg-brown text-white px-4 py-2 rounded-lg hover:bg-brown-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Generate Key
            </button>
            <button
              onClick={() => setShowNewKeyForm(false)}
              className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* API Keys List */}
      <div className="bg-white dark:bg-brown-dark rounded-lg border border-gray-200 dark:border-brown overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-brown">
          <h3 className="text-lg font-semibold text-brown dark:text-beige-light">Existing API Keys</h3>
        </div>

        {apiKeys.length === 0 ? (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            <KeyIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No API keys generated yet</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-brown">
            {apiKeys.map((key) => (
              <div key={key.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-brown dark:text-beige-light">{key.name}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        key.is_active 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {key.is_active ? 'Active' : 'Revoked'}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                      <p>Key: {key.key_prefix}••••••••</p>
                      <p>Rate Limit: {key.rate_limit} requests/15min</p>
                      <p>Usage: {key.usage_count} requests</p>
                      <p>Last Used: {formatDate(key.last_used_at)}</p>
                      <p>Created: {formatDate(key.created_at)}</p>
                      {key.expires_at && (
                        <p>Expires: {formatDate(key.expires_at)}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {key.is_active && (
                      <button
                        onClick={() => revokeApiKey(key.id)}
                        className="text-red-600 hover:text-red-700 p-2"
                        title="Revoke API Key"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default APIKeyManager;
