/**
 * JotForm AI Assistant Configuration for UrbanEdge Real Estate
 * This file contains the configuration and setup for integrating JotForm AI with our property API
 */

// API Configuration
export const JOTFORM_CONFIG = {
  formId: '01977ef6126a721ca60420e562e7a5468ca6',
  apiKey: 'urbanedge-ai-assistant-key-2024',
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  embedOptions: {
    skipWelcome: 1,
    maximizable: 1,
    theme: 'urbanedge'
  }
};

// AI Agent Prompt Configuration
export const AI_AGENT_PROMPT = `
You are a helpful and knowledgeable real estate assistant for UrbanEdge Properties, Nigeria's premier real estate company. Your role is to help customers find their perfect property by searching our comprehensive database.

## Your Capabilities:
- Search properties by location, price, bedrooms, bathrooms, and features
- Provide detailed property information including images and descriptions
- Find properties near specific locations using coordinates
- Suggest similar properties based on customer preferences
- Explain Nigerian real estate market trends and pricing

## API Endpoints Available:
1. Property Search: GET /api/properties/search
2. Property Filter: GET /api/properties/filter  
3. Property Details: GET /api/properties/{id}
4. Location Search: GET /api/search/location
5. Nearby Properties: GET /api/search/nearby
6. Filter Metadata: GET /api/metadata/filters

## How to Use APIs:

### Basic Property Search:
- For general searches: GET /api/properties/search?location={location}&limit=10
- For specific requirements: GET /api/properties/search?location={location}&bedrooms={num}&maxPrice={price}

### Price Filtering:
- Use Nigerian Naira values: GET /api/properties/search?minPrice=1000000&maxPrice=5000000
- For price ranges: GET /api/properties/filter?priceRange=1000000-5000000

### Location Searches:
- City/area search: GET /api/search/location?query={location_name}
- Nearby properties: GET /api/search/nearby?latitude={lat}&longitude={lng}&radius=10

### Property Types:
- apartment, house, condo, land, commercial, townhouse

### Sale Types:
- rent, buy

## Response Guidelines:
1. Always format prices in Nigerian Naira (₦) with proper formatting
2. Provide clear, helpful descriptions of properties
3. Include key details: bedrooms, bathrooms, square footage, location
4. Mention notable features like swimming pools, gyms, security
5. Be conversational and friendly while remaining professional
6. If no properties match, suggest alternative searches or broader criteria

## Example Interactions:

Customer: "I need a 3-bedroom apartment in Lagos under ₦5 million"
API Call: GET /api/properties/search?location=Lagos&bedrooms=3&propertyType=apartment&maxPrice=5000000

Customer: "Show me houses for rent in Victoria Island"
API Call: GET /api/search/location?query=Victoria Island&propertyType=house&saleType=rent

Customer: "What properties are near this location?" (with coordinates)
API Call: GET /api/search/nearby?latitude=6.4281&longitude=3.4219&radius=5

## Important Notes:
- Always use the API to get current property data
- Don't make up property information
- If API returns no results, suggest broader search criteria
- Prices are in Nigerian Naira (₦)
- Be helpful in explaining Nigerian real estate terminology
- Encourage customers to contact UrbanEdge for viewings and more information

When external data is required, make an API request to get the most current property information.
`;

// API Request Configuration
export const API_REQUEST_CONFIG = {
  method: 'GET',
  headers: {
    'X-API-Key': JOTFORM_CONFIG.apiKey,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000 // 10 seconds timeout
};

// Response Template for Property Results
export const PROPERTY_RESPONSE_TEMPLATE = `
{if properties.length > 0}
Found {properties.length} properties matching your criteria:

{properties.map(property => `
🏠 **${property.title}**
📍 ${property.location}${property.neighborhood ? ', ' + property.neighborhood : ''}
💰 ${property.price.formatted}
🛏️ ${property.bedrooms} bedroom${property.bedrooms !== 1 ? 's' : ''} • 🚿 ${property.bathrooms} bathroom${property.bathrooms !== 1 ? 's' : ''}
📐 ${property.squareFeet} sq ft
🏢 ${property.propertyType} for ${property.saleType}

${property.description ? property.description.substring(0, 150) + '...' : ''}

${property.features && property.features.length > 0 ? '✨ Features: ' + property.features.join(', ') : ''}

---
`).join('')}

Would you like more details about any of these properties, or would you like me to search with different criteria?

💡 **Tip**: I can also help you find properties near specific locations, within certain price ranges, or with particular features like swimming pools or gyms.
{else}
I couldn't find any properties matching your exact criteria. Let me suggest some alternatives:

🔍 **Try these options:**
- Expand your search area to nearby neighborhoods
- Increase your budget range slightly
- Consider different property types (apartments, houses, condos)
- Look at both rent and buy options

Would you like me to search with broader criteria, or do you have different requirements in mind?
{endif}
`;

// Error Response Template
export const ERROR_RESPONSE_TEMPLATE = `
I apologize, but I'm having trouble accessing our property database right now. This might be a temporary issue.

🔄 **What you can do:**
- Try your search again in a moment
- Contact our team directly for immediate assistance
- Browse our website at urbanedge.com

📞 **Need immediate help?**
Our real estate experts are available to assist you with your property search and answer any questions about the Nigerian real estate market.

Is there anything else I can help you with in the meantime?
`;

// JotForm Integration Helper Functions
export const jotformHelpers = {
  /**
   * Initialize JotForm with UrbanEdge configuration
   */
  initializeJotForm: () => {
    if (typeof window !== 'undefined' && window.JotFormAgent) {
      window.JotFormAgent.configure({
        apiEndpoints: {
          propertySearch: `${JOTFORM_CONFIG.apiBaseUrl}/properties/search`,
          propertyFilter: `${JOTFORM_CONFIG.apiBaseUrl}/properties/filter`,
          propertyDetails: `${JOTFORM_CONFIG.apiBaseUrl}/properties`,
          locationSearch: `${JOTFORM_CONFIG.apiBaseUrl}/search/location`,
          nearbySearch: `${JOTFORM_CONFIG.apiBaseUrl}/search/nearby`,
          metadata: `${JOTFORM_CONFIG.apiBaseUrl}/metadata/filters`,
          suggestions: `${JOTFORM_CONFIG.apiBaseUrl}/search/suggestions`
        },
        apiKey: JOTFORM_CONFIG.apiKey,
        headers: API_REQUEST_CONFIG.headers,
        agentPrompt: AI_AGENT_PROMPT,
        responseTemplate: PROPERTY_RESPONSE_TEMPLATE,
        errorTemplate: ERROR_RESPONSE_TEMPLATE
      });
      
      console.log('✅ JotForm AI Assistant configured for UrbanEdge');
      return true;
    }
    return false;
  },

  /**
   * Load JotForm script dynamically
   */
  loadJotFormScript: () => {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`script[src*="${JOTFORM_CONFIG.formId}"]`)) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://cdn.jotfor.ms/agent/embedjs/${JOTFORM_CONFIG.formId}/embed.js?skipWelcome=${JOTFORM_CONFIG.embedOptions.skipWelcome}&maximizable=${JOTFORM_CONFIG.embedOptions.maximizable}`;
      script.async = true;
      script.onload = () => resolve(true);
      script.onerror = () => reject(new Error('Failed to load JotForm script'));
      
      document.head.appendChild(script);
    });
  },

  /**
   * Format property data for AI consumption
   */
  formatPropertyForAI: (property) => {
    return {
      id: property.id,
      title: property.title,
      location: property.location,
      neighborhood: property.neighborhood,
      price: {
        raw: property.price,
        formatted: new Intl.NumberFormat('en-NG', {
          style: 'currency',
          currency: 'NGN',
          minimumFractionDigits: 0
        }).format(property.price)
      },
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      squareFeet: property.squareFeet || property.square_feet,
      propertyType: property.propertyType || property.property_type,
      saleType: property.saleType || property.sale_type,
      description: property.description,
      features: property.features || [],
      images: property.images || [],
      coordinates: property.coordinates
    };
  },

  /**
   * Test API connectivity
   */
  testAPIConnection: async () => {
    try {
      const response = await fetch(`${JOTFORM_CONFIG.apiBaseUrl}/health`, {
        headers: API_REQUEST_CONFIG.headers
      });
      
      if (response.ok) {
        console.log('✅ UrbanEdge API connection successful');
        return true;
      } else {
        console.error('❌ UrbanEdge API connection failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('❌ UrbanEdge API connection error:', error);
      return false;
    }
  }
};

export default JOTFORM_CONFIG;
