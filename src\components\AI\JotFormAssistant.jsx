import { useEffect, useRef, useState } from 'react';
import { ChatBubbleLeftRightIcon, XMarkIcon } from '@heroicons/react/24/outline';

/**
 * JotForm AI Assistant Component for UrbanEdge Real Estate
 * Integrates with the property search API to provide intelligent property assistance
 */
const JotFormAssistant = ({ 
  isVisible = false, 
  onToggle,
  position = 'bottom-right',
  apiBaseUrl = 'http://localhost:3001/api'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const scriptRef = useRef(null);
  const containerRef = useRef(null);

  // JotForm configuration
  const jotformConfig = {
    formId: '01977ef6126a721ca60420e562e7a5468ca6',
    apiKey: 'urbanedge-ai-assistant-key-2024',
    apiBaseUrl: apiBaseUrl,
    skipWelcome: 1,
    maximizable: 1
  };

  useEffect(() => {
    // Load JotForm script dynamically
    const loadJotFormScript = () => {
      if (scriptRef.current) return; // Already loaded

      const script = document.createElement('script');
      script.src = `https://cdn.jotfor.ms/agent/embedjs/${jotformConfig.formId}/embed.js?skipWelcome=${jotformConfig.skipWelcome}&maximizable=${jotformConfig.maximizable}`;
      script.async = true;
      script.onload = () => {
        setIsLoaded(true);
        configureJotFormAPI();
      };
      script.onerror = () => {
        console.error('Failed to load JotForm AI Assistant script');
      };

      document.head.appendChild(script);
      scriptRef.current = script;
    };

    if (isVisible && !isLoaded) {
      loadJotFormScript();
    }

    return () => {
      // Cleanup script when component unmounts
      if (scriptRef.current) {
        document.head.removeChild(scriptRef.current);
        scriptRef.current = null;
      }
    };
  }, [isVisible, isLoaded]);

  // Configure JotForm to use our API endpoints
  const configureJotFormAPI = () => {
    // Wait for JotForm to be available
    const checkJotForm = () => {
      if (window.JotFormAgent) {
        // Configure API endpoints for the AI assistant
        window.JotFormAgent.configure({
          apiEndpoints: {
            propertySearch: `${jotformConfig.apiBaseUrl}/properties/search`,
            propertyFilter: `${jotformConfig.apiBaseUrl}/properties/filter`,
            propertyDetails: `${jotformConfig.apiBaseUrl}/properties`,
            locationSearch: `${jotformConfig.apiBaseUrl}/search/location`,
            nearbySearch: `${jotformConfig.apiBaseUrl}/search/nearby`,
            metadata: `${jotformConfig.apiBaseUrl}/metadata/filters`,
            suggestions: `${jotformConfig.apiBaseUrl}/search/suggestions`
          },
          apiKey: jotformConfig.apiKey,
          headers: {
            'X-API-Key': jotformConfig.apiKey,
            'Content-Type': 'application/json'
          }
        });

        console.log('✅ JotForm AI Assistant configured with UrbanEdge API');
      } else {
        // Retry after 100ms if JotForm is not ready
        setTimeout(checkJotForm, 100);
      }
    };

    checkJotForm();
  };

  const toggleAssistant = () => {
    setIsExpanded(!isExpanded);
    if (onToggle) {
      onToggle(!isExpanded);
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6';
      case 'bottom-right':
        return 'bottom-6 right-6';
      case 'top-left':
        return 'top-6 left-6';
      case 'top-right':
        return 'top-6 right-6';
      default:
        return 'bottom-6 right-6';
    }
  };

  if (!isVisible) return null;

  return (
    <>
      {/* AI Assistant Toggle Button */}
      <div className={`fixed ${getPositionClasses()} z-50`}>
        <button
          onClick={toggleAssistant}
          className="bg-brown hover:bg-brown-dark text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-brown/30"
          title="UrbanEdge AI Property Assistant"
        >
          {isExpanded ? (
            <XMarkIcon className="h-6 w-6" />
          ) : (
            <ChatBubbleLeftRightIcon className="h-6 w-6" />
          )}
        </button>

        {/* Loading indicator */}
        {!isLoaded && isExpanded && (
          <div className="absolute bottom-16 right-0 bg-white dark:bg-brown-dark rounded-lg shadow-lg p-4 min-w-[300px]">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-brown"></div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Loading AI Assistant...
              </span>
            </div>
          </div>
        )}
      </div>

      {/* JotForm Container */}
      {isExpanded && isLoaded && (
        <div 
          ref={containerRef}
          className={`fixed ${getPositionClasses()} z-40`}
          style={{
            bottom: position.includes('bottom') ? '5rem' : 'auto',
            top: position.includes('top') ? '5rem' : 'auto',
            right: position.includes('right') ? '1.5rem' : 'auto',
            left: position.includes('left') ? '1.5rem' : 'auto',
          }}
        >
          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-2xl border border-gray-200 dark:border-brown overflow-hidden">
            {/* Header */}
            <div className="bg-brown text-white p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <ChatBubbleLeftRightIcon className="h-5 w-5" />
                <div>
                  <h3 className="font-semibold text-sm">UrbanEdge AI Assistant</h3>
                  <p className="text-xs opacity-90">Ask me about properties in Nigeria</p>
                </div>
              </div>
              <button
                onClick={toggleAssistant}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* JotForm Embed Container */}
            <div 
              id="jotform-ai-container"
              className="w-[400px] h-[500px] bg-white dark:bg-brown-dark"
            >
              {/* JotForm will inject its content here */}
            </div>

            {/* Footer */}
            <div className="bg-gray-50 dark:bg-brown/20 p-3 text-center">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Powered by UrbanEdge AI • Real Estate Intelligence
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-black/20 z-30"
          onClick={toggleAssistant}
        />
      )}
    </>
  );
};

export default JotFormAssistant;
