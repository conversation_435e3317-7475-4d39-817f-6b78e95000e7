{"name": "StarterPackCode", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "api": "node api/server.js", "dev:full": "concurrently \"npm run api\" \"npm run dev\"", "start": "node api/server.js", "setup:api": "node setup-api.js", "test:api": "node test-api.js", "test:api:simple": "curl -H \"X-API-Key: urbanedge-ai-assistant-key-2024\" http://localhost:3001/api/health", "test:jotform": "echo 'Visit http://localhost:5173/ai-test to test JotForm integration' && npm run dev"}, "dependencies": {"@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.0.1", "@material-tailwind/react": "^2.1.10", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.5.4", "leaflet": "^1.9.4", "lucide-react": "^0.503.0", "openai": "^5.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.4", "react-icons": "^5.3.0", "react-leaflet": "^4.2.1", "react-leaflet-cluster": "^2.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.27.0", "react-router-hash-link": "^2.4.3", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.30.2", "react-toggle-dark-mode": "^1.1.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.4", "express": "^4.18.2", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "concurrently": "^8.2.2", "node-fetch": "^3.3.2"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "vite": "^5.4.8"}}