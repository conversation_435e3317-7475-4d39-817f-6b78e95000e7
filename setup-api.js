#!/usr/bin/env node

/**
 * Setup script for UrbanEdge AI Assistant API
 * This script installs dependencies and sets up the API server
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Setting up UrbanEdge AI Assistant API...\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Check if API directory exists
if (!fs.existsSync('api')) {
  console.error('❌ Error: API directory not found. Please ensure the API files are in place.');
  process.exit(1);
}

try {
  // Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  
  // Check environment variables
  console.log('\n🔧 Checking environment configuration...');
  
  if (!fs.existsSync('.env')) {
    console.log('⚠️  Warning: .env file not found. Creating from template...');
    
    const envTemplate = `# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_SUPABASE_SERVICE_ROLE_SECRET=your_supabase_service_role_key_here

# API Configuration
JOTFORM_API_KEY=urbanedge-ai-assistant-key-2024
EXTERNAL_API_KEY=your_external_api_key_here
NODE_ENV=development
PORT=3001

# Application Configuration
VITE_APP_NAME=UrbanEdge
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_PROPERTY_EMBEDDINGS=true
VITE_ENABLE_VOICE_CHAT=false

# Development Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
`;
    
    fs.writeFileSync('.env', envTemplate);
    console.log('✅ Created .env template. Please update with your actual values.');
  }
  
  // Check if Supabase is configured
  const envContent = fs.readFileSync('.env', 'utf8');
  if (envContent.includes('your_supabase_url_here')) {
    console.log('⚠️  Warning: Please update your Supabase configuration in .env file');
  } else {
    console.log('✅ Supabase configuration found');
  }
  
  // Create logs directory
  if (!fs.existsSync('logs')) {
    fs.mkdirSync('logs');
    console.log('✅ Created logs directory');
  }
  
  // Test API server startup
  console.log('\n🧪 Testing API server startup...');
  
  try {
    // Start server in test mode
    const testProcess = execSync('timeout 5s node api/server.js || true', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });
    
    if (testProcess.includes('UrbanEdge AI Assistant API running')) {
      console.log('✅ API server test successful');
    } else {
      console.log('⚠️  API server test completed (check for any errors above)');
    }
  } catch (error) {
    console.log('⚠️  API server test completed (this is normal for the timeout test)');
  }
  
  // Display next steps
  console.log('\n🎉 Setup completed successfully!\n');
  console.log('📋 Next steps:');
  console.log('1. Update your .env file with actual Supabase credentials');
  console.log('2. Run database migrations: npx supabase db push');
  console.log('3. Start the API server: npm run api');
  console.log('4. Start the frontend: npm run dev');
  console.log('5. Or start both: npm run dev:full\n');
  
  console.log('🔗 API Endpoints will be available at:');
  console.log('- Health check: http://localhost:3001/api/health');
  console.log('- Property search: http://localhost:3001/api/properties/search');
  console.log('- Documentation: See API_DOCUMENTATION.md\n');
  
  console.log('🔑 Default API Key for testing:');
  console.log('urbanedge-ai-assistant-key-2024\n');
  
  console.log('⚠️  Security Note:');
  console.log('Generate new API keys for production use through the admin dashboard.');
  
} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}
