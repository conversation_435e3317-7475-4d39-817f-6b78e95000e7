-- Create API keys table for managing external access to the AI assistant API
-- This allows secure access from JotForm and other external services

-- Create api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL, -- Descriptive name for the API key
  key_hash TEXT NOT NULL UNIQUE, -- Hashed version of the API key
  key_prefix TEXT NOT NULL, -- First 8 characters for identification
  permissions JSONB DEFAULT '{"read": true, "write": false}'::jsonb, -- Permissions object
  rate_limit INTEGER DEFAULT 100, -- Requests per 15-minute window
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration
  last_used_at TIMESTAMP WITH TIME ZONE,
  usage_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on api_keys table
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Create policies for api_keys table
-- Only admins can manage API keys
CREATE POLICY "Admins can manage API keys" ON api_keys
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE id = auth.uid() 
      AND raw_user_meta_data->>'is_admin' = 'true'
    )
  );

-- Create function to generate API key
CREATE OR REPLACE FUNCTION generate_api_key(
  key_name TEXT,
  permissions_obj JSONB DEFAULT '{"read": true, "write": false}'::jsonb,
  rate_limit_val INTEGER DEFAULT 100,
  expires_in_days INTEGER DEFAULT NULL
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_key TEXT;
  key_hash TEXT;
  key_prefix TEXT;
  expires_date TIMESTAMP WITH TIME ZONE;
  new_api_key_id UUID;
  is_admin BOOLEAN;
BEGIN
  -- Check if user is admin
  SELECT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE id = auth.uid() 
    AND raw_user_meta_data->>'is_admin' = 'true'
  ) INTO is_admin;
  
  IF NOT is_admin THEN
    RAISE EXCEPTION 'Only administrators can generate API keys';
  END IF;
  
  -- Generate a random API key (32 characters)
  new_key := 'urbanedge_' || encode(gen_random_bytes(16), 'hex');
  
  -- Create hash of the key for storage
  key_hash := encode(digest(new_key, 'sha256'), 'hex');
  
  -- Get first 8 characters for identification
  key_prefix := substring(new_key from 1 for 8);
  
  -- Calculate expiration date if specified
  IF expires_in_days IS NOT NULL THEN
    expires_date := now() + (expires_in_days || ' days')::interval;
  END IF;
  
  -- Insert the API key record
  INSERT INTO api_keys (
    name,
    key_hash,
    key_prefix,
    permissions,
    rate_limit,
    expires_at,
    created_by
  ) VALUES (
    key_name,
    key_hash,
    key_prefix,
    permissions_obj,
    rate_limit_val,
    expires_date,
    auth.uid()
  ) RETURNING id INTO new_api_key_id;
  
  -- Return the API key details (including the actual key - only shown once)
  RETURN jsonb_build_object(
    'id', new_api_key_id,
    'name', key_name,
    'api_key', new_key,
    'key_prefix', key_prefix,
    'permissions', permissions_obj,
    'rate_limit', rate_limit_val,
    'expires_at', expires_date,
    'created_at', now(),
    'warning', 'Store this API key securely. It will not be shown again.'
  );
END;
$$;

-- Create function to validate API key
CREATE OR REPLACE FUNCTION validate_api_key(
  provided_key TEXT
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  key_hash TEXT;
  api_key_record RECORD;
BEGIN
  -- Hash the provided key
  key_hash := encode(digest(provided_key, 'sha256'), 'hex');
  
  -- Find the API key record
  SELECT * FROM api_keys 
  WHERE key_hash = validate_api_key.key_hash 
  AND is_active = true
  AND (expires_at IS NULL OR expires_at > now())
  INTO api_key_record;
  
  -- If no valid key found
  IF api_key_record IS NULL THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Invalid or expired API key'
    );
  END IF;
  
  -- Update usage statistics
  UPDATE api_keys 
  SET 
    last_used_at = now(),
    usage_count = usage_count + 1,
    updated_at = now()
  WHERE id = api_key_record.id;
  
  -- Return validation result
  RETURN jsonb_build_object(
    'valid', true,
    'id', api_key_record.id,
    'name', api_key_record.name,
    'permissions', api_key_record.permissions,
    'rate_limit', api_key_record.rate_limit
  );
END;
$$;

-- Create function to revoke API key
CREATE OR REPLACE FUNCTION revoke_api_key(
  key_id UUID
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_admin BOOLEAN;
  key_exists BOOLEAN;
BEGIN
  -- Check if user is admin
  SELECT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE id = auth.uid() 
    AND raw_user_meta_data->>'is_admin' = 'true'
  ) INTO is_admin;
  
  IF NOT is_admin THEN
    RAISE EXCEPTION 'Only administrators can revoke API keys';
  END IF;
  
  -- Check if key exists
  SELECT EXISTS (
    SELECT 1 FROM api_keys WHERE id = key_id
  ) INTO key_exists;
  
  IF NOT key_exists THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'API key not found'
    );
  END IF;
  
  -- Deactivate the API key
  UPDATE api_keys 
  SET 
    is_active = false,
    updated_at = now()
  WHERE id = key_id;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'API key revoked successfully'
  );
END;
$$;

-- Create function to list API keys (for admin dashboard)
CREATE OR REPLACE FUNCTION list_api_keys()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_admin BOOLEAN;
  keys_data JSONB;
BEGIN
  -- Check if user is admin
  SELECT EXISTS (
    SELECT 1 FROM auth.users 
    WHERE id = auth.uid() 
    AND raw_user_meta_data->>'is_admin' = 'true'
  ) INTO is_admin;
  
  IF NOT is_admin THEN
    RAISE EXCEPTION 'Only administrators can list API keys';
  END IF;
  
  -- Get API keys data (excluding the actual key hash)
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', id,
      'name', name,
      'key_prefix', key_prefix,
      'permissions', permissions,
      'rate_limit', rate_limit,
      'is_active', is_active,
      'expires_at', expires_at,
      'last_used_at', last_used_at,
      'usage_count', usage_count,
      'created_at', created_at
    ) ORDER BY created_at DESC
  ) FROM api_keys INTO keys_data;
  
  RETURN COALESCE(keys_data, '[]'::jsonb);
END;
$$;

-- Insert a default API key for JotForm integration
-- This will be used for initial setup - admins should generate new keys in production
INSERT INTO api_keys (
  name,
  key_hash,
  key_prefix,
  permissions,
  rate_limit,
  created_by
) VALUES (
  'JotForm AI Assistant - Default',
  encode(digest('urbanedge-ai-assistant-key-2024', 'sha256'), 'hex'),
  'urbanedg',
  '{"read": true, "write": false, "search": true, "metadata": true}'::jsonb,
  200,
  (SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true' LIMIT 1)
) ON CONFLICT (key_hash) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires ON api_keys(expires_at);

-- Add comments for documentation
COMMENT ON TABLE api_keys IS 'API keys for external access to the UrbanEdge AI assistant API';
COMMENT ON FUNCTION generate_api_key IS 'Generate a new API key for external services';
COMMENT ON FUNCTION validate_api_key IS 'Validate an API key and return permissions';
COMMENT ON FUNCTION revoke_api_key IS 'Revoke an API key by setting it inactive';
COMMENT ON FUNCTION list_api_keys IS 'List all API keys for admin dashboard';
