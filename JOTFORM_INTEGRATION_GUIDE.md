# JotForm AI Assistant Integration Guide for UrbanEdge

## 🎯 Overview

This guide provides step-by-step instructions for integrating your JotForm AI Assistant with the UrbanEdge real estate API. The integration allows the AI assistant to search and display property information directly from your Supabase database.

## 🚀 Quick Start

### 1. **Start Your API Server**

```bash
# Start the UrbanEdge API server
npm run api

# Or start both API and frontend
npm run dev:full
```

### 2. **Test the Integration**

Visit the test page to verify everything is working:
```
http://localhost:5173/ai-test
```

### 3. **Configure JotForm Dashboard**

Follow the detailed configuration steps below.

## 🔧 JotForm Configuration Steps

### **Step 1: Access JotForm Dashboard**

1. Log into your JotForm account
2. Navigate to your form: `01977ef6126a721ca60420e562e7a5468ca6`
3. Go to **Settings** → **AI Assistant**

### **Step 2: Configure API Integration**

#### **API Endpoints Configuration**

Set up these endpoints in your JotForm AI Assistant:

**Base URL**: `http://localhost:3001/api` (development) or `https://your-domain.com/api` (production)

**Primary Endpoints**:
- **Property Search**: `GET /properties/search`
- **Property Filter**: `GET /properties/filter`
- **Location Search**: `GET /search/location`
- **Nearby Properties**: `GET /search/nearby`
- **Property Details**: `GET /properties/{id}`
- **Metadata**: `GET /metadata/filters`

#### **Authentication Headers**

Add these headers to all API requests:
```
X-API-Key: urbanedge-ai-assistant-key-2024
Content-Type: application/json
Accept: application/json
```

### **Step 3: Configure AI Agent Prompt**

Copy and paste this prompt into your JotForm AI Assistant configuration:

```
You are UrbanEdge AI, a specialized real estate assistant for Nigeria's premier property platform. Your mission is to help customers find their perfect property using our comprehensive database.

## Core Capabilities:
🏠 Search properties by location, price, size, and features
💰 Filter by budget ranges in Nigerian Naira (₦)
📍 Find properties near specific locations
🔍 Provide detailed property information
📊 Explain market trends and pricing

## API Usage Guidelines:

### Property Search Patterns:
- General search: "properties in [location]" → GET /properties/search?location=[location]
- Budget search: "under ₦[amount]" → GET /properties/search?maxPrice=[amount]
- Specific needs: "[bedrooms] bedroom [type] in [location]" → GET /properties/search?location=[location]&bedrooms=[num]&propertyType=[type]

### Location Intelligence:
- City/area queries → GET /search/location?query=[location]
- "Near [landmark]" → GET /search/nearby with coordinates
- Suggestions → GET /search/suggestions?query=[partial_location]

### Price Handling:
- Always use Nigerian Naira (₦)
- Convert millions: "5M" = 5,000,000
- Price ranges: GET /properties/filter?priceRange=[min]-[max]

## Response Format:
1. **Property Listings**: Show title, location, price (₦), bedrooms/bathrooms, key features
2. **No Results**: Suggest broader criteria or alternative locations
3. **Follow-up**: Always ask if they want more details or different search criteria

## Example Interactions:

Customer: "3-bedroom apartments in Lagos under ₦5 million"
Action: GET /properties/search?location=Lagos&bedrooms=3&propertyType=apartment&maxPrice=5000000

Customer: "Houses for rent in Victoria Island"
Action: GET /search/location?query=Victoria Island&propertyType=house&saleType=rent

Customer: "Properties near Lekki Phase 1"
Action: GET /search/location?query=Lekki Phase 1

## Important Rules:
✅ Always use the API for current data
✅ Format prices in Nigerian Naira with proper commas
✅ Be conversational but professional
✅ Suggest viewings and contact information
✅ Handle "no results" gracefully with alternatives
❌ Never invent property details
❌ Don't quote outdated prices or information

When customers ask about properties, ALWAYS make an API call to get the most current information.
```

### **Step 4: Configure Response Templates**

#### **Property Listing Template**:
```
{if properties && properties.length > 0}
🏠 **Found {properties.length} properties for you:**

{properties.map(p => `
**${p.title}**
📍 ${p.location}${p.neighborhood ? ', ' + p.neighborhood : ''}
💰 ${p.price.formatted}
🛏️ ${p.bedrooms} bed • 🚿 ${p.bathrooms} bath • 📐 ${p.squareFeet} sq ft
🏢 ${p.propertyType} for ${p.saleType}

${p.description ? p.description.substring(0, 120) + '...' : ''}
${p.features && p.features.length > 0 ? '✨ ' + p.features.slice(0, 3).join(', ') : ''}

---
`).join('')}

💡 Would you like more details about any property, or shall I search with different criteria?
{else}
🔍 **No properties found matching your criteria.**

Let me help you find alternatives:
• Try expanding your search area
• Consider adjusting your budget range  
• Look at different property types
• Check both rent and buy options

What would you like to adjust in your search?
{endif}
```

#### **Error Template**:
```
🔧 **I'm having trouble accessing our property database right now.**

Here's what you can do:
• Try your search again in a moment
• Contact our team directly: +234-XXX-XXXX
• Visit our website: urbanedge.com
• Email us: <EMAIL>

Our real estate experts are ready to help you find the perfect property! 🏠
```

### **Step 5: Configure Welcome Message**

Set this as your welcome message:
```
👋 Hi! I'm UrbanEdge AI, your personal real estate assistant. I can help you find properties across Nigeria. What kind of property are you looking for?
```

### **Step 6: Configure Appearance**

#### **Theme Colors** (matching UrbanEdge brand):
- **Primary Color**: `#574C3F` (Brown)
- **Accent Color**: `#B9A590` (Taupe)
- **Background**: `#F6F3EC` (Beige Light)
- **Text Color**: `#36302A` (Brown Dark)

#### **Settings**:
- **Max Results**: 10
- **Timeout**: 15 seconds
- **Retry Attempts**: 2
- **Placeholder**: "Ask me about properties in Lagos, Abuja, or anywhere in Nigeria..."

## 🧪 Testing Your Integration

### **Test Queries**

Use these queries to test your JotForm AI Assistant:

1. **Basic Search**:
   - "Show me apartments in Lagos"
   - "I need a house in Abuja"

2. **Budget Filtering**:
   - "3-bedroom apartments under ₦5 million"
   - "Houses between ₦2M and ₦10M"

3. **Location-Specific**:
   - "Properties in Victoria Island"
   - "Houses for rent in Lekki"

4. **Feature-Based**:
   - "Properties with swimming pools"
   - "Apartments with gyms"

5. **Nearby Search**:
   - "Properties near Lekki Phase 1"
   - "Houses close to Victoria Island"

### **Expected Responses**

The AI should:
- ✅ Make API calls to your UrbanEdge endpoints
- ✅ Display property listings with Nigerian Naira formatting
- ✅ Show property details (bedrooms, bathrooms, features)
- ✅ Handle "no results" gracefully
- ✅ Suggest alternative searches

## 🔒 Security Configuration

### **API Key Management**

1. **Development**: Use `urbanedge-ai-assistant-key-2024`
2. **Production**: Generate new keys via admin dashboard
3. **Monitoring**: Track usage in the admin panel

### **CORS Settings**

Ensure your API server allows requests from JotForm domains:
- `https://www.jotform.com`
- `https://jotform.com`
- `https://eu-api.jotform.com`
- `https://api.jotform.com`

## 🚨 Troubleshooting

### **Common Issues**

#### **AI Assistant Not Loading**
- Check if JotForm script is loaded in browser console
- Verify form ID: `01977ef6126a721ca60420e562e7a5468ca6`
- Ensure API server is running on port 3001

#### **API Connection Errors**
- Test API health: `http://localhost:3001/api/health`
- Verify API key: `urbanedge-ai-assistant-key-2024`
- Check CORS configuration

#### **No Property Results**
- Verify database has property data
- Test API endpoints manually
- Check Supabase connection

#### **Authentication Errors**
- Confirm API key in headers
- Check rate limiting (100 requests/15 minutes)
- Verify Supabase service role permissions

### **Debug Steps**

1. **Check API Server**:
   ```bash
   npm run test:api
   ```

2. **Test Individual Endpoints**:
   ```bash
   curl -H "X-API-Key: urbanedge-ai-assistant-key-2024" http://localhost:3001/api/health
   ```

3. **Browser Console**:
   - Look for JotForm loading messages
   - Check for API request/response logs
   - Verify configuration injection

4. **Test Page**:
   - Visit: `http://localhost:5173/ai-test`
   - Run endpoint tests
   - Check status indicators

## 📞 Support

### **Getting Help**

1. **Documentation**: Review `API_DOCUMENTATION.md`
2. **Test Page**: Use `/ai-test` for diagnostics
3. **Logs**: Check browser console and server logs
4. **API Testing**: Use `npm run test:api`

### **Contact Information**

For technical support with the JotForm integration:
- Check the implementation files in `/src/components/AI/`
- Review configuration in `/src/lib/jotformConfig.js`
- Test API connectivity with the provided tools

## 🎉 Success Checklist

- ✅ API server running on port 3001
- ✅ JotForm script loading successfully
- ✅ API key authentication working
- ✅ Property search returning results
- ✅ Nigerian Naira formatting correct
- ✅ Location searches functional
- ✅ Error handling working properly
- ✅ AI responses are helpful and accurate

Your UrbanEdge AI Assistant is now ready to help customers find their perfect property! 🏠
