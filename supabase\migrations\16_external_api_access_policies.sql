-- Enhanced RLS policies for external API access
-- This allows the AI assistant API to access property data securely

-- Create a special role for API access
-- Note: This would typically be done at the database level by a superuser
-- For now, we'll use the service role with proper function-based access

-- Create function to check if request is from valid API
CREATE OR REPLACE FUNCTION is_valid_api_request()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current role is the service role
  -- This indicates the request is coming through our API server
  RETURN current_setting('role') = 'service_role';
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$;

-- Update properties policies to allow API access
-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "API can read properties" ON properties;
DROP POLICY IF EXISTS "Service role can read properties" ON properties;

-- Create new policy for API access to properties
CREATE POLICY "API can read properties" ON properties
  FOR SELECT
  TO service_role
  USING (true);

-- Update property_images policies
DROP POLICY IF EXISTS "API can read property images" ON property_images;

CREATE POLICY "API can read property images" ON property_images
  FOR SELECT
  TO service_role
  USING (true);

-- Update property_types policies
DROP POLICY IF EXISTS "API can read property types" ON property_types;

CREATE POLICY "API can read property types" ON property_types
  FOR SELECT
  TO service_role
  USING (true);

-- Update sale_types policies
DROP POLICY IF EXISTS "API can read sale types" ON sale_types;

CREATE POLICY "API can read sale types" ON sale_types
  FOR SELECT
  TO service_role
  USING (true);

-- Update features policies
DROP POLICY IF EXISTS "API can read features" ON features;

CREATE POLICY "API can read features" ON features
  FOR SELECT
  TO service_role
  USING (true);

-- Update property_features policies
DROP POLICY IF EXISTS "API can read property features" ON property_features;

CREATE POLICY "API can read property features" ON property_features
  FOR SELECT
  TO service_role
  USING (true);

-- Create enhanced filter function for API access
CREATE OR REPLACE FUNCTION api_filter_properties(
  location TEXT DEFAULT NULL,
  min_price NUMERIC DEFAULT NULL,
  max_price NUMERIC DEFAULT NULL,
  min_bedrooms INTEGER DEFAULT NULL,
  max_bedrooms INTEGER DEFAULT NULL,
  min_bathrooms INTEGER DEFAULT NULL,
  max_bathrooms INTEGER DEFAULT NULL,
  property_type_ids UUID[] DEFAULT NULL,
  feature_ids UUID[] DEFAULT NULL,
  sale_type_id UUID DEFAULT NULL,
  limit_val INTEGER DEFAULT 20,
  offset_val INTEGER DEFAULT 0,
  sort_by TEXT DEFAULT 'newest'
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- This allows the function to bypass RLS
AS $$
DECLARE
  query_text TEXT;
  count_query_text TEXT;
  where_clauses TEXT := '1=1';
  order_by_clause TEXT;
  total_count INTEGER;
  properties_data JSONB;
BEGIN
  -- Validate inputs
  IF limit_val > 100 THEN
    limit_val := 100; -- Maximum 100 properties per request
  END IF;
  
  IF limit_val < 1 THEN
    limit_val := 20;
  END IF;
  
  -- Build WHERE clauses
  IF location IS NOT NULL THEN
    where_clauses := where_clauses || ' AND (p.location ILIKE ''%' || location || '%'' OR p.neighborhood ILIKE ''%' || location || '%'')';
  END IF;
  
  IF min_price IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.price >= ' || min_price;
  END IF;
  
  IF max_price IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.price <= ' || max_price;
  END IF;
  
  IF min_bedrooms IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.bedrooms >= ' || min_bedrooms;
  END IF;
  
  IF max_bedrooms IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.bedrooms <= ' || max_bedrooms;
  END IF;
  
  IF min_bathrooms IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.bathrooms >= ' || min_bathrooms;
  END IF;
  
  IF max_bathrooms IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.bathrooms <= ' || max_bathrooms;
  END IF;
  
  IF property_type_ids IS NOT NULL AND array_length(property_type_ids, 1) > 0 THEN
    where_clauses := where_clauses || ' AND p.property_type_id = ANY(''' || property_type_ids::text || ''')';
  END IF;
  
  IF sale_type_id IS NOT NULL THEN
    where_clauses := where_clauses || ' AND p.sale_type_id = ''' || sale_type_id || '''';
  END IF;
  
  -- Handle feature filtering
  IF feature_ids IS NOT NULL AND array_length(feature_ids, 1) > 0 THEN
    where_clauses := where_clauses || ' AND EXISTS (
      SELECT 1 FROM property_features pf 
      WHERE pf.property_id = p.id 
      AND pf.feature_id = ANY(''' || feature_ids::text || ''')
    )';
  END IF;
  
  -- Build ORDER BY clause
  CASE sort_by
    WHEN 'price_asc' THEN order_by_clause := 'p.price ASC';
    WHEN 'price_desc' THEN order_by_clause := 'p.price DESC';
    WHEN 'bedrooms_asc' THEN order_by_clause := 'p.bedrooms ASC';
    WHEN 'bedrooms_desc' THEN order_by_clause := 'p.bedrooms DESC';
    WHEN 'newest' THEN order_by_clause := 'p.created_at DESC';
    WHEN 'oldest' THEN order_by_clause := 'p.created_at ASC';
    ELSE order_by_clause := 'p.created_at DESC';
  END CASE;
  
  -- Get total count
  count_query_text := '
    SELECT COUNT(*)
    FROM properties p
    JOIN property_types pt ON p.property_type_id = pt.id
    JOIN sale_types st ON p.sale_type_id = st.id
    WHERE ' || where_clauses;
  
  EXECUTE count_query_text INTO total_count;
  
  -- Build the main query with all related data
  query_text := '
    SELECT jsonb_agg(
      jsonb_build_object(
        ''id'', p.id,
        ''title'', p.title,
        ''location'', p.location,
        ''neighborhood'', p.neighborhood,
        ''price'', p.price,
        ''bedrooms'', p.bedrooms,
        ''bathrooms'', p.bathrooms,
        ''square_feet'', p.square_feet,
        ''description'', p.description,
        ''floor_plan_url'', p.floor_plan_url,
        ''latitude'', p.latitude,
        ''longitude'', p.longitude,
        ''property_type'', pt.name,
        ''sale_type'', st.name,
        ''created_at'', p.created_at,
        ''updated_at'', p.updated_at,
        ''thumbnail_url'', (
          SELECT pi.image_url 
          FROM property_images pi 
          WHERE pi.property_id = p.id 
          ORDER BY pi."order" ASC 
          LIMIT 1
        ),
        ''images'', (
          SELECT COALESCE(jsonb_agg(pi.image_url ORDER BY pi."order"), ''[]''::jsonb)
          FROM property_images pi 
          WHERE pi.property_id = p.id
        ),
        ''features'', (
          SELECT COALESCE(jsonb_agg(f.name), ''[]''::jsonb)
          FROM property_features pf
          JOIN features f ON pf.feature_id = f.id
          WHERE pf.property_id = p.id
        )
      ) ORDER BY ' || order_by_clause || '
    )
    FROM (
      SELECT p.*
      FROM properties p
      JOIN property_types pt ON p.property_type_id = pt.id
      JOIN sale_types st ON p.sale_type_id = st.id
      WHERE ' || where_clauses || '
      ORDER BY ' || order_by_clause || '
      LIMIT ' || limit_val || ' OFFSET ' || offset_val || '
    ) p
    JOIN property_types pt ON p.property_type_id = pt.id
    JOIN sale_types st ON p.sale_type_id = st.id';
  
  EXECUTE query_text INTO properties_data;
  
  -- Return structured result
  RETURN jsonb_build_object(
    'properties', COALESCE(properties_data, '[]'::jsonb),
    'total_count', total_count,
    'limit', limit_val,
    'offset', offset_val,
    'has_more', (offset_val + limit_val) < total_count
  );
END;
$$;

-- Create API-specific property details function
CREATE OR REPLACE FUNCTION api_get_property_details(property_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  property_data JSONB;
BEGIN
  -- Get comprehensive property details
  SELECT jsonb_build_object(
    'id', p.id,
    'title', p.title,
    'location', p.location,
    'neighborhood', p.neighborhood,
    'price', p.price,
    'bedrooms', p.bedrooms,
    'bathrooms', p.bathrooms,
    'square_feet', p.square_feet,
    'description', p.description,
    'floor_plan_url', p.floor_plan_url,
    'latitude', p.latitude,
    'longitude', p.longitude,
    'property_type', pt.name,
    'sale_type', st.name,
    'created_at', p.created_at,
    'updated_at', p.updated_at,
    'images', (
      SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
          'url', pi.image_url,
          'order', pi."order"
        ) ORDER BY pi."order"
      ), '[]'::jsonb)
      FROM property_images pi 
      WHERE pi.property_id = p.id
    ),
    'features', (
      SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
          'id', f.id,
          'name', f.name
        )
      ), '[]'::jsonb)
      FROM property_features pf
      JOIN features f ON pf.feature_id = f.id
      WHERE pf.property_id = p.id
    )
  )
  FROM properties p
  JOIN property_types pt ON p.property_type_id = pt.id
  JOIN sale_types st ON p.sale_type_id = st.id
  WHERE p.id = property_id
  INTO property_data;
  
  RETURN property_data;
END;
$$;

-- Create API-specific metadata function
CREATE OR REPLACE FUNCTION api_get_filter_options()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'property_types', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'id', pt.id,
          'name', pt.name
        ) ORDER BY pt.name
      )
      FROM property_types pt
    ),
    'sale_types', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'id', st.id,
          'name', st.name
        ) ORDER BY st.name
      )
      FROM sale_types st
    ),
    'features', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'id', f.id,
          'name', f.name
        ) ORDER BY f.name
      )
      FROM features f
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant necessary permissions to service role
-- Note: These would typically be set by a database administrator
-- GRANT USAGE ON SCHEMA public TO service_role;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO service_role;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Add comments for documentation
COMMENT ON FUNCTION api_filter_properties IS 'Enhanced property filtering function for external API access';
COMMENT ON FUNCTION api_get_property_details IS 'Get detailed property information for external API access';
COMMENT ON FUNCTION api_get_filter_options IS 'Get filter options for external API access';
