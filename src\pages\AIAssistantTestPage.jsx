import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { 
  ChatBubbleLeftRightIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ClockIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import JotFormAssistant from '../components/AI/JotFormAssistant';
import { jotformHelpers } from '../lib/jotformConfig';

/**
 * AI Assistant Test Page
 * This page allows testing and demonstration of the JotForm AI Assistant integration
 */
const AIAssistantTestPage = () => {
  const [apiStatus, setApiStatus] = useState('checking');
  const [jotformStatus, setJotformStatus] = useState('checking');
  const [testResults, setTestResults] = useState([]);
  const [showAssistant, setShowAssistant] = useState(true);

  useEffect(() => {
    checkAPIStatus();
    checkJotFormStatus();
  }, []);

  const checkAPIStatus = async () => {
    try {
      const isConnected = await jotformHelpers.testAPIConnection();
      setApiStatus(isConnected ? 'connected' : 'error');
    } catch (error) {
      setApiStatus('error');
    }
  };

  const checkJotFormStatus = () => {
    const checkInterval = setInterval(() => {
      if (window.JotFormAgent || window.JF) {
        setJotformStatus('loaded');
        clearInterval(checkInterval);
      }
    }, 500);

    // Timeout after 10 seconds
    setTimeout(() => {
      if (jotformStatus === 'checking') {
        setJotformStatus('error');
        clearInterval(checkInterval);
      }
    }, 10000);
  };

  const runTestQuery = async (query, expectedEndpoint) => {
    const testResult = {
      query,
      expectedEndpoint,
      timestamp: new Date().toISOString(),
      status: 'running'
    };

    setTestResults(prev => [...prev, testResult]);

    try {
      // Simulate API call
      const apiUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'}${expectedEndpoint}`;
      const response = await fetch(apiUrl, {
        headers: {
          'X-API-Key': 'urbanedge-ai-assistant-key-2024',
          'Content-Type': 'application/json'
        }
      });

      const success = response.ok;
      
      setTestResults(prev => 
        prev.map(test => 
          test.timestamp === testResult.timestamp 
            ? { ...test, status: success ? 'success' : 'error', response: response.status }
            : test
        )
      );
    } catch (error) {
      setTestResults(prev => 
        prev.map(test => 
          test.timestamp === testResult.timestamp 
            ? { ...test, status: 'error', error: error.message }
            : test
        )
      );
    }
  };

  const testQueries = [
    {
      query: "Show me 3-bedroom apartments in Lagos",
      endpoint: "/properties/search?location=Lagos&bedrooms=3&propertyType=apartment"
    },
    {
      query: "Houses under ₦5 million",
      endpoint: "/properties/search?maxPrice=5000000&propertyType=house"
    },
    {
      query: "Properties for rent in Victoria Island",
      endpoint: "/search/location?query=Victoria Island&saleType=rent"
    },
    {
      query: "Properties near Lekki Phase 1",
      endpoint: "/search/location?query=Lekki Phase 1"
    }
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
      case 'loaded':
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'checking':
      case 'running':
        return <ClockIcon className="h-5 w-5 text-yellow-500 animate-spin" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'connected': return 'Connected';
      case 'loaded': return 'Loaded';
      case 'success': return 'Success';
      case 'error': return 'Error';
      case 'checking': return 'Checking...';
      case 'running': return 'Running...';
      default: return 'Unknown';
    }
  };

  return (
    <>
      <Helmet>
        <title>AI Assistant Test | UrbanEdge Real Estate</title>
        <meta name="description" content="Test and demonstrate the UrbanEdge AI Assistant integration with JotForm" />
      </Helmet>

      <div className="min-h-screen bg-beige-light dark:bg-brown py-12">
        <div className="container mx-auto px-6">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <CpuChipIcon className="h-8 w-8 text-brown" />
              <h1 className="text-4xl font-bold text-brown dark:text-beige-light">
                AI Assistant Integration Test
              </h1>
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Test and demonstrate the JotForm AI Assistant integration with UrbanEdge property database
            </p>
          </div>

          {/* Status Dashboard */}
          <div className="grid md:grid-cols-2 gap-6 mb-12">
            {/* API Status */}
            <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-brown/10 rounded-lg">
                  <CpuChipIcon className="h-6 w-6 text-brown" />
                </div>
                <h3 className="text-lg font-semibold text-brown dark:text-beige-light">
                  UrbanEdge API Status
                </h3>
              </div>
              
              <div className="flex items-center gap-3">
                {getStatusIcon(apiStatus)}
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {getStatusText(apiStatus)}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                Connection to property database API
              </p>
            </div>

            {/* JotForm Status */}
            <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-brown/10 rounded-lg">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-brown" />
                </div>
                <h3 className="text-lg font-semibold text-brown dark:text-beige-light">
                  JotForm AI Status
                </h3>
              </div>
              
              <div className="flex items-center gap-3">
                {getStatusIcon(jotformStatus)}
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {getStatusText(jotformStatus)}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                JotForm AI Assistant script loading
              </p>
            </div>
          </div>

          {/* Test Queries */}
          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6 mb-12">
            <h3 className="text-lg font-semibold text-brown dark:text-beige-light mb-6">
              API Endpoint Tests
            </h3>
            
            <div className="grid gap-4 mb-6">
              {testQueries.map((test, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-brown/20 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-800 dark:text-gray-200">
                      "{test.query}"
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                      {test.endpoint}
                    </p>
                  </div>
                  <button
                    onClick={() => runTestQuery(test.query, test.endpoint)}
                    className="bg-brown text-white px-4 py-2 rounded-lg hover:bg-brown-dark transition-colors"
                  >
                    Test
                  </button>
                </div>
              ))}
            </div>

            {/* Test Results */}
            {testResults.length > 0 && (
              <div className="border-t border-gray-200 dark:border-brown pt-6">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  Test Results
                </h4>
                <div className="space-y-3">
                  {testResults.map((result, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-brown/20 rounded">
                      {getStatusIcon(result.status)}
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {result.query}
                      </span>
                      {result.response && (
                        <span className="text-xs text-gray-500 ml-auto">
                          HTTP {result.response}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-white dark:bg-brown-dark rounded-lg shadow-sm border border-gray-200 dark:border-brown p-6 mb-12">
            <h3 className="text-lg font-semibold text-brown dark:text-beige-light mb-4">
              How to Test the AI Assistant
            </h3>
            
            <div className="space-y-4 text-gray-700 dark:text-gray-300">
              <div className="flex items-start gap-3">
                <span className="bg-brown text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">1</span>
                <p>Click the AI Assistant button in the bottom-right corner</p>
              </div>
              
              <div className="flex items-start gap-3">
                <span className="bg-brown text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">2</span>
                <p>Try these sample queries:</p>
              </div>
              
              <div className="ml-9 space-y-2">
                <p className="text-sm bg-gray-100 dark:bg-brown/20 p-2 rounded font-mono">
                  "Show me 3-bedroom apartments in Lagos under ₦5 million"
                </p>
                <p className="text-sm bg-gray-100 dark:bg-brown/20 p-2 rounded font-mono">
                  "I need houses for rent in Victoria Island"
                </p>
                <p className="text-sm bg-gray-100 dark:bg-brown/20 p-2 rounded font-mono">
                  "What properties do you have with swimming pools?"
                </p>
                <p className="text-sm bg-gray-100 dark:bg-brown/20 p-2 rounded font-mono">
                  "Find me properties near Lekki Phase 1"
                </p>
              </div>
              
              <div className="flex items-start gap-3">
                <span className="bg-brown text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold">3</span>
                <p>The AI should respond with relevant property listings from your database</p>
              </div>
            </div>
          </div>

          {/* AI Assistant Toggle */}
          <div className="text-center">
            <button
              onClick={() => setShowAssistant(!showAssistant)}
              className="bg-brown text-white px-6 py-3 rounded-lg hover:bg-brown-dark transition-colors flex items-center gap-2 mx-auto"
            >
              <ChatBubbleLeftRightIcon className="h-5 w-5" />
              {showAssistant ? 'Hide' : 'Show'} AI Assistant
            </button>
          </div>
        </div>
      </div>

      {/* JotForm AI Assistant */}
      <JotFormAssistant
        isVisible={showAssistant}
        onToggle={setShowAssistant}
        position="bottom-right"
      />
    </>
  );
};

export default AIAssistantTestPage;
